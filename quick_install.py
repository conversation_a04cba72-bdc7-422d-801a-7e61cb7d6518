#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
无线APK安装工具
支持命令行参数和交互式安装
"""

import os
import subprocess
import glob
import sys
import argparse
import time
import json
from datetime import datetime

# 设备记录文件路径
DEVICE_HISTORY_FILE = os.path.join(os.path.expanduser('~'), '.adb_device_history.json')

def load_device_history():
    """加载设备连接历史"""
    try:
        if os.path.exists(DEVICE_HISTORY_FILE):
            with open(DEVICE_HISTORY_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
    except Exception as e:
        print(f"加载设备历史失败: {e}")
    return {}

def save_device_history(history):
    """保存设备连接历史"""
    try:
        with open(DEVICE_HISTORY_FILE, 'w', encoding='utf-8') as f:
            json.dump(history, f, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"保存设备历史失败: {e}")

def add_device_to_history(ip, port=5555, device_name="Unknown Device"):
    """添加设备到历史记录"""
    history = load_device_history()
    device_key = f"{ip}:{port}"

    history[device_key] = {
        'ip': ip,
        'port': port,
        'device_name': device_name,
        'last_connected': datetime.now().isoformat(),
        'connection_count': history.get(device_key, {}).get('connection_count', 0) + 1
    }

    save_device_history(history)
    print(f"✅ 设备 {device_key} 已添加到历史记录")

def get_recent_devices(limit=5):
    """获取最近连接的设备"""
    history = load_device_history()
    if not history:
        return []

    # 按最后连接时间排序
    sorted_devices = sorted(
        history.items(),
        key=lambda x: x[1].get('last_connected', ''),
        reverse=True
    )

    return sorted_devices[:limit]

def try_connect_recent_devices(adb_path):
    """尝试连接最近使用的设备"""
    recent_devices = get_recent_devices()
    if not recent_devices:
        print("📱 没有找到历史连接记录")
        return None

    print("🔍 尝试连接最近使用的设备...")
    for device_key, device_info in recent_devices:
        ip = device_info['ip']
        port = device_info['port']
        device_name = device_info.get('device_name', 'Unknown')
        last_connected = device_info.get('last_connected', 'Unknown')

        print(f"📱 尝试连接: {device_name} ({ip}:{port}) - 上次连接: {last_connected[:19]}")

        if connect_device(adb_path, ip, port):
            print(f"✅ 成功连接到历史设备: {ip}:{port}")
            # 更新连接时间
            add_device_to_history(ip, port, device_name)
            return f"{ip}:{port}"
        else:
            print(f"❌ 连接失败: {ip}:{port}")

    print("⚠️ 所有历史设备连接失败，需要手动输入")
    return None

def find_adb_path():
    """查找ADB工具路径"""
    # 常见的ADB路径
    common_paths = [
        # Android Studio默认路径
        os.path.expanduser('~/AppData/Local/Android/Sdk/platform-tools/adb.exe'),
        'C:/Android/Sdk/platform-tools/adb.exe',
        'C:/Users/<USER>/AppData/Local/Android/Sdk/platform-tools/adb.exe' % os.getenv('USERNAME', ''),

        # 其他可能的路径
        'C:/Program Files/Android/Android Studio/sdk/platform-tools/adb.exe',
        'C:/Program Files (x86)/Android/android-sdk/platform-tools/adb.exe',
        'D:/Android/Sdk/platform-tools/adb.exe',
        'E:/Android/Sdk/platform-tools/adb.exe',

        # 当前目录
        './adb.exe',
        './platform-tools/adb.exe',
    ]

    # 首先尝试直接调用adb
    try:
        subprocess.run(['adb', 'version'], capture_output=True, check=True)
        return 'adb'
    except (subprocess.CalledProcessError, FileNotFoundError):
        pass

    # 在常见路径中查找
    for path in common_paths:
        if os.path.exists(path):
            print(f"找到ADB工具: {path}")
            return path

    return None

def add_to_path(adb_dir):
    """将ADB目录添加到系统PATH"""
    try:
        # 获取当前PATH
        current_path = os.environ.get('PATH', '')

        # 检查是否已经在PATH中
        if adb_dir.lower() in current_path.lower():
            print(f"ADB目录已在PATH中: {adb_dir}")
            return True

        # 添加到当前会话的PATH
        new_path = adb_dir + os.pathsep + current_path
        os.environ['PATH'] = new_path

        # 尝试永久添加到系统PATH (需要管理员权限)
        try:
            import winreg
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, 'Environment', 0, winreg.KEY_ALL_ACCESS)
            try:
                current_user_path, _ = winreg.QueryValueEx(key, 'PATH')
            except FileNotFoundError:
                current_user_path = ''

            if adb_dir.lower() not in current_user_path.lower():
                new_user_path = current_user_path + os.pathsep + adb_dir if current_user_path else adb_dir
                winreg.SetValueEx(key, 'PATH', 0, winreg.REG_EXPAND_SZ, new_user_path)
                print(f"已将ADB目录添加到用户PATH: {adb_dir}")
                print("注意: 需要重启命令行窗口才能生效")

            winreg.CloseKey(key)
            return True

        except Exception as e:
            print(f"无法永久添加到PATH (需要管理员权限): {e}")
            print(f"已临时添加到当前会话PATH: {adb_dir}")
            return True

    except Exception as e:
        print(f"添加PATH失败: {e}")
        return False

# 全局ADB路径变量
ADB_PATH = None

def init_adb():
    """初始化ADB工具"""
    global ADB_PATH

    # 查找ADB路径
    ADB_PATH = find_adb_path()

    if not ADB_PATH:
        print("❌ 未找到ADB工具")
        print("\n请选择以下选项之一:")
        print("1. 安装Android Studio (推荐)")
        print("2. 下载独立的ADB工具")
        print("3. 手动指定ADB路径")

        choice = input("\n请输入选择 (1-3): ").strip()

        if choice == '3':
            manual_path = input("请输入adb.exe的完整路径: ").strip()
            if os.path.exists(manual_path):
                ADB_PATH = manual_path
                # 添加到PATH
                adb_dir = os.path.dirname(manual_path)
                add_to_path(adb_dir)
            else:
                print("❌ 指定的路径不存在")
                return False
        else:
            print("请先安装ADB工具后再运行此脚本")
            return False

    # 如果找到了ADB但不是直接可调用的，添加到PATH
    if ADB_PATH != 'adb' and os.path.exists(ADB_PATH):
        adb_dir = os.path.dirname(ADB_PATH)
        add_to_path(adb_dir)
        # 重新测试是否可以直接调用
        try:
            subprocess.run(['adb', 'version'], capture_output=True, check=True)
            ADB_PATH = 'adb'  # 现在可以直接调用了
        except:
            pass  # 继续使用完整路径

    return True

def run_adb_command(cmd):
    """执行ADB命令并返回结果"""
    if not ADB_PATH:
        return False, "ADB工具未初始化"

    try:
        if ADB_PATH == 'adb':
            adb_cmd = ['adb'] + cmd
        else:
            adb_cmd = [ADB_PATH] + cmd

        # 使用bytes模式避免编码问题，然后手动解码
        result = subprocess.run(adb_cmd, capture_output=True, check=True)

        # 尝试多种编码方式解码输出
        stdout_text = ""
        stderr_text = ""

        for encoding in ['utf-8', 'gbk', 'cp936', 'latin1']:
            try:
                stdout_text = result.stdout.decode(encoding).strip()
                break
            except UnicodeDecodeError:
                continue
        else:
            # 如果所有编码都失败，使用错误处理
            stdout_text = result.stdout.decode('utf-8', errors='replace').strip()

        for encoding in ['utf-8', 'gbk', 'cp936', 'latin1']:
            try:
                stderr_text = result.stderr.decode(encoding).strip()
                break
            except UnicodeDecodeError:
                continue
        else:
            stderr_text = result.stderr.decode('utf-8', errors='replace').strip()

        return True, stdout_text
    except subprocess.CalledProcessError as e:
        # 处理错误输出的编码
        error_text = ""
        if e.stderr:
            for encoding in ['utf-8', 'gbk', 'cp936', 'latin1']:
                try:
                    error_text = e.stderr.decode(encoding).strip()
                    break
                except UnicodeDecodeError:
                    continue
            else:
                error_text = e.stderr.decode('utf-8', errors='replace').strip()
        elif e.stdout:
            for encoding in ['utf-8', 'gbk', 'cp936', 'latin1']:
                try:
                    error_text = e.stdout.decode(encoding).strip()
                    break
                except UnicodeDecodeError:
                    continue
            else:
                error_text = e.stdout.decode('utf-8', errors='replace').strip()

        return False, error_text
    except FileNotFoundError:
        return False, f"ADB工具未找到: {ADB_PATH}"

def list_devices():
    """列出所有连接的设备"""
    success, output = run_adb_command(['devices'])
    if not success:
        return []

    devices = []
    lines = output.split('\n')[1:]  # 跳过标题行
    for line in lines:
        if line.strip() and '\t' in line:
            device_id, status = line.split('\t')
            devices.append((device_id.strip(), status.strip()))

    return devices

def enable_wireless_debug(device_id=None, port=5555):
    """启用无线调试"""
    cmd = ['tcpip', str(port)]
    if device_id:
        cmd = ['-s', device_id] + cmd

    success, output = run_adb_command(cmd)
    if success:
        time.sleep(2)  # 等待设备重启调试服务
    return success, output

def find_available_port():
    """查找可用端口"""
    import socket

    # 常用的ADB端口范围
    common_ports = [5555, 5556, 5557, 5558, 5559, 37000, 37001, 37002]

    for port in common_ports:
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(1)
                result = s.connect_ex(('127.0.0.1', port))
                if result != 0:  # 端口未被占用
                    return port
        except:
            continue

    # 如果常用端口都被占用，随机选择一个
    import random
    return random.randint(5555, 5600)

def get_device_wireless_port(device_id):
    """获取设备当前的无线调试端口"""
    # 尝试从设备属性中获取端口信息
    success, output = run_adb_command(['-s', device_id, 'shell', 'getprop', 'service.adb.tcp.port'])
    if success and output.strip() and output.strip() != '-1':
        try:
            return int(output.strip())
        except ValueError:
            pass

    return None

def connect_device(ip, port=5555):
    """连接到无线设备"""
    success, output = run_adb_command(['connect', f'{ip}:{port}'])
    return success and 'connected' in output.lower(), output

def try_connect_multiple_ports(ip, ports=[5555, 5556, 5557, 5558, 37000]):
    """尝试连接多个端口"""
    for port in ports:
        print(f"尝试连接端口 {port}...")
        success, output = connect_device(ip, port)
        if success:
            return True, port, output

    return False, None, "所有端口连接失败"

def pair_device(ip, port, pairing_code):
    """配对设备 (Android 11+)"""
    print(f"🔗 正在配对 {ip}:{port}...")
    success, output = run_adb_command(['pair', f'{ip}:{port}', pairing_code])
    return success and 'successfully paired' in output.lower(), output

def try_pairing_then_connect(ip, port=5555):
    """尝试配对然后连接 (Android 11+)"""
    print(f"\n📱 检测到 {ip}:{port}")
    print("这可能是Android 11+设备的无线调试地址")

    # 先尝试直接连接
    print("🔍 尝试直接连接...")
    success, output = connect_device(ip, port)
    if success:
        print("✅ 直接连接成功!")
        return True, port

    # 如果直接连接失败，尝试配对
    if "failed to authenticate" in output.lower() or "connection refused" in output.lower():
        print("🔐 需要配对认证")
        print("\n请在手机上操作:")
        print("设置 → 开发者选项 → 无线调试 → 使用配对码配对设备")
        print(f"确认显示的地址是: {ip}:{port}")

        pairing_code = input("请输入手机显示的6位配对码: ").strip()
        if not pairing_code or len(pairing_code) != 6 or not pairing_code.isdigit():
            print("❌ 配对码必须是6位数字")
            return False, None

        # 执行配对
        success, pair_output = pair_device(ip, port, pairing_code)
        if not success:
            print(f"❌ 配对失败: {pair_output}")
            return False, None

        print("✅ 配对成功!")

        # 配对成功后尝试连接
        print("🔗 正在连接...")
        success, connect_output = connect_device(ip, port)
        if success:
            print("✅ 连接成功!")
            # 添加设备到历史记录
            add_device_to_history(ip, port, f"Device-{ip}")
            return True, port
        else:
            print(f"❌ 连接失败: {connect_output}")
            return False, None
    else:
        print(f"❌ 连接失败: {output}")
        return False, None

def pair_device(ip, port, pairing_code):
    """配对设备 (Android 11+)"""
    success, output = run_adb_command(['pair', f'{ip}:{port}', pairing_code])
    return success and 'successfully paired' in output.lower(), output

def check_android_version(device_id):
    """检查Android版本"""
    success, output = run_adb_command(['-s', device_id, 'shell', 'getprop', 'ro.build.version.sdk'])
    if success and output.strip().isdigit():
        return int(output.strip())
    return 0

def wireless_pairing_flow(ip):
    """Android 11+ 无线配对流程"""
    print("\n📱 Android 11+ 无线调试配对")
    print("=" * 40)
    print("请在手机上操作:")
    print("1. 设置 → 开发者选项 → 无线调试")
    print("2. 点击 '使用配对码配对设备'")
    print("3. 记录显示的IP地址、端口和配对码")
    print()

    # 获取配对信息
    pair_ip = input("请输入配对IP地址 (如果与连接IP相同可直接回车): ").strip()
    if not pair_ip:
        pair_ip = ip

    pair_port = input("请输入配对端口: ").strip()
    if not pair_port:
        print("❌ 配对端口不能为空")
        return False, None

    pairing_code = input("请输入6位配对码: ").strip()
    if not pairing_code or len(pairing_code) != 6:
        print("❌ 配对码必须是6位数字")
        return False, None

    # 执行配对
    print(f"🔗 正在配对 {pair_ip}:{pair_port}...")
    success, output = pair_device(pair_ip, pair_port, pairing_code)

    if success:
        print("✅ 配对成功!")

        # 获取连接端口
        connect_port = input("请输入连接端口 (通常显示在配对成功后): ").strip()
        if connect_port:
            return True, int(connect_port)
        else:
            # 尝试常用端口
            return True, 5555
    else:
        print(f"❌ 配对失败: {output}")
        return False, None

def install_apk(apk_path, device_id=None):
    """安装APK文件 - 强制覆盖安装"""
    if not os.path.exists(apk_path):
        return False, f"APK文件不存在: {apk_path}"

    # 首先尝试覆盖安装
    cmd = ['install', '-r', apk_path]
    if device_id:
        cmd = ['-s', device_id] + cmd

    success, output = run_adb_command(cmd)

    # 如果因为签名不匹配失败，尝试强制覆盖安装
    if not success and ("INSTALL_FAILED_UPDATE_INCOMPATIBLE" in output or
                       "signatures do not match" in output or
                       "签名" in output):
        print("🔄 检测到签名不匹配，尝试强制覆盖安装...")

        # 尝试多种强制安装参数组合
        force_options = [
            ['-r', '-d'],  # 替换 + 允许降级
            ['-r', '-t'],  # 替换 + 允许测试APK
            ['-r', '-d', '-t'],  # 替换 + 降级 + 测试APK
            ['-r', '--force-queryable'],  # 替换 + 强制可查询
            ['-r', '-d', '-t', '--force-queryable'],  # 所有参数
        ]

        for i, options in enumerate(force_options, 1):
            print(f"🔄 尝试方案 {i}: {' '.join(options)}")
            cmd_force = ['install'] + options + [apk_path]
            if device_id:
                cmd_force = ['-s', device_id] + cmd_force

            success, output = run_adb_command(cmd_force)
            if success:
                print(f"✅ 方案 {i} 成功!")
                break
            else:
                print(f"❌ 方案 {i} 失败: {output.split(':')[-1].strip() if ':' in output else output}")

        # 如果所有方案都失败，提示用户手动处理
        if not success:
            print("⚠️  所有强制安装方案都失败了")
            print("💡 建议: 请在手机上手动卸载应用后重新安装，或检查应用是否正在运行")

    return success, output

def find_apk_files():
    """查找当前目录的APK文件"""
    return glob.glob("*.apk")

def select_apk_file():
    """选择APK文件"""
    apk_files = find_apk_files()

    if not apk_files:
        apk_file = input("请输入APK文件路径: ").strip()
        if not apk_file or not os.path.exists(apk_file):
            print("❌ APK文件不存在")
            return None
        return apk_file
    elif len(apk_files) == 1:
        print(f"🎯 自动选择APK: {apk_files[0]}")
        return apk_files[0]
    else:
        print("📦 找到多个APK文件:")
        for i, apk in enumerate(apk_files):
            print(f"  {i+1}. {apk}")

        while True:
            try:
                choice = int(input("请选择APK编号: ")) - 1
                if 0 <= choice < len(apk_files):
                    return apk_files[choice]
                else:
                    print("❌ 无效选择")
            except ValueError:
                print("❌ 请输入数字")

def main():
    parser = argparse.ArgumentParser(description='无线APK安装工具')
    parser.add_argument('-r', '--list-devices', action='store_true', help='列出所有连接的设备')
    parser.add_argument('--ip', help='设备IP地址')
    parser.add_argument('--apk', help='APK文件路径')
    parser.add_argument('--port', type=int, default=5555, help='端口号 (默认: 5555)')
    parser.add_argument('--device', help='指定设备ID')
    parser.add_argument('--pair-only', action='store_true', help='仅配对设备，不安装APK')
    parser.add_argument('--history', action='store_true', help='显示设备连接历史')
    parser.add_argument('--clear-history', action='store_true', help='清除设备连接历史')

    args = parser.parse_args()

    # 显示设备历史
    if args.history:
        recent_devices = get_recent_devices(10)  # 显示最近10个设备
        if not recent_devices:
            print("📱 没有设备连接历史")
        else:
            print("📱 设备连接历史:")
            print("-" * 80)
            for i, (device_key, device_info) in enumerate(recent_devices, 1):
                device_name = device_info.get('device_name', 'Unknown')
                last_connected = device_info.get('last_connected', 'Unknown')[:19]
                connection_count = device_info.get('connection_count', 0)
                print(f"{i:2d}. {device_name}")
                print(f"    地址: {device_key}")
                print(f"    最后连接: {last_connected}")
                print(f"    连接次数: {connection_count}")
                print()
        return

    # 清除设备历史
    if args.clear_history:
        try:
            if os.path.exists(DEVICE_HISTORY_FILE):
                os.remove(DEVICE_HISTORY_FILE)
                print("✅ 设备连接历史已清除")
            else:
                print("📱 没有设备连接历史")
        except Exception as e:
            print(f"❌ 清除历史失败: {e}")
        return

    # 初始化ADB工具
    if not init_adb():
        return

    # 列出设备模式
    if args.list_devices:
        devices = list_devices()
        if not devices:
            print("No")
        else:
            for device_id, status in devices:
                print(f"{device_id}\t{status}")
        return

    # 仅配对模式
    if args.pair_only and args.ip:
        port = args.port
        success, port = try_pairing_then_connect(args.ip, port)
        if success:
            print(f"✅ 配对和连接成功: {args.ip}:{port}")
        else:
            print("❌ 配对失败")
        return

    # 命令行安装模式
    if args.ip and args.apk:
        # 检查设备参数逻辑
        devices = list_devices()
        if args.device:
            # 检查指定的设备是否存在
            device_exists = any(device_id == args.device for device_id, _ in devices)
            if not device_exists:
                print(f"❌ 指定的设备不存在: {args.device}")
                return

            # 如果只有一个设备且指定了设备参数，提示无需添加
            if len(devices) == 1:
                print(f"💡 提示: 当前仅有一个设备，无需指定 --device 参数")
        else:
            # 没有指定设备，使用第一个设备
            if devices:
                args.device = devices[0][0]
                print(f"🎯 自动选择第一个设备: {args.device}")

        port = args.port

        # 启用无线调试
        if args.device:
            # 检查设备当前是否已有无线调试端口
            current_port = get_device_wireless_port(args.device)
            if current_port:
                port = current_port
                print(f"检测到设备已启用无线调试，端口: {port}")
            else:
                # 查找可用端口
                if port == 5555:  # 如果使用默认端口，尝试找一个可用的
                    port = find_available_port()

                success, output = enable_wireless_debug(args.device, port)
                if not success:
                    print(f"启用无线调试失败: {output}")
                    return
                print(f"已启用无线调试，端口: {port}")

        # 连接设备
        success, output = connect_device(args.ip, port)
        if not success:
            # 检查是否需要配对
            if "failed to authenticate" in output.lower() or "connection refused" in output.lower():
                print("🔐 检测到需要配对的设备")
                success, port = try_pairing_then_connect(args.ip, port)
                if not success:
                    return
            else:
                # 尝试多个常用端口
                print("尝试其他常用端口...")
                success, actual_port, output = try_connect_multiple_ports(args.ip)
                if success:
                    port = actual_port
                else:
                    # 最后尝试配对
                    print("尝试配对流程...")
                    success, port = try_pairing_then_connect(args.ip, 5555)
                    if not success:
                        print(f"连接失败: {output}")
                        return

        print(f"✅ 连接成功: {args.ip}:{port}")

        # 添加设备到历史记录
        add_device_to_history(args.ip, port, f"Device-{args.ip}")

        # 安装APK
        device_target = f"{args.ip}:{port}"
        success, output = install_apk(args.apk, device_target)

        if success and "Success" in output:
            print("✅ 安装成功")
        else:
            print(f"❌ 安装失败: {output}")

        return

    # 交互式模式
    print("🚀 无线APK安装工具")
    print("=" * 30)

    # 首先尝试连接历史设备
    adb_path = find_adb_path()
    if adb_path:
        connected_device = try_connect_recent_devices(adb_path)
        if connected_device:
            # 成功连接到历史设备，直接进入APK选择流程
            print(f"🎯 已连接到设备: {connected_device}")

            # 选择APK文件
            apk_file = select_apk_file()
            if not apk_file:
                return

            # 安装APK
            success, output = install_apk(apk_file, connected_device)
            if success and "Success" in output:
                print("✅ 安装成功")
            else:
                print(f"❌ 安装失败: {output}")
            return

    # 检查设备
    devices = list_devices()
    if not devices:
        print("❌ 未检测到设备，请通过USB连接手机并开启调试模式")
        print("\n💡 或者如果您的设备支持无线调试，可以使用:")
        print("python quick_install.py --pair-only --ip <设备IP>")

        # 显示历史设备选项
        recent_devices = get_recent_devices()
        if recent_devices:
            print("\n📱 历史连接设备:")
            for i, (device_key, device_info) in enumerate(recent_devices):
                device_name = device_info.get('device_name', 'Unknown')
                last_connected = device_info.get('last_connected', 'Unknown')[:19]
                print(f"  {i+1}. {device_name} ({device_key}) - {last_connected}")

            try:
                choice = input("\n是否尝试连接历史设备? (输入编号或回车跳过): ").strip()
                if choice.isdigit():
                    choice_idx = int(choice) - 1
                    if 0 <= choice_idx < len(recent_devices):
                        device_key, device_info = recent_devices[choice_idx]
                        ip, port = device_key.split(':')
                        if connect_device(adb_path, ip, int(port)):
                            print(f"✅ 成功连接到历史设备: {device_key}")
                            add_device_to_history(ip, int(port), device_info.get('device_name', 'Unknown'))

                            # 选择APK文件
                            apk_file = select_apk_file()
                            if apk_file:
                                success, output = install_apk(apk_file, device_key)
                                if success and "Success" in output:
                                    print("✅ 安装成功")
                                else:
                                    print(f"❌ 安装失败: {output}")
                            return
                        else:
                            print(f"❌ 连接失败: {device_key}")
            except (ValueError, KeyboardInterrupt):
                pass

        return

    print(f"📱 检测到 {len(devices)} 个设备:")
    for i, (device_id, status) in enumerate(devices):
        print(f"  {i+1}. {device_id} ({status})")

    # 选择设备
    if len(devices) == 1:
        selected_device = devices[0][0]
        print(f"🎯 自动选择设备: {selected_device}")
    else:
        while True:
            try:
                choice = int(input("请选择设备编号: ")) - 1
                if 0 <= choice < len(devices):
                    selected_device = devices[choice][0]
                    break
                else:
                    print("❌ 无效选择")
            except ValueError:
                print("❌ 请输入数字")

    # 启用无线调试
    print("启用无线调试...")

    # 检查设备当前是否已有无线调试端口
    current_port = get_device_wireless_port(selected_device)
    if current_port:
        port = current_port
        print(f"检测到设备已启用无线调试，端口: {port}")
    else:
        # 查找可用端口
        port = find_available_port()
        success, output = enable_wireless_debug(selected_device, port)
        if not success:
            print(f"启用无线调试失败: {output}")
            return
        print(f"无线调试已启用，端口: {port}")

    # 获取IP地址
    ip = input("请输入手机IP地址: ").strip()
    if not ip:
        print("IP地址不能为空")
        return

    # 连接设备
    print(f"🌐 连接到 {ip}:{port}...")
    success, output = connect_device(ip, port)
    if not success:
        # 检查是否需要配对 (Android 11+)
        if "failed to authenticate" in output.lower() or "connection refused" in output.lower():
            print("🔐 检测到需要配对的设备")
            success, port = try_pairing_then_connect(ip, port)
            if not success:
                return
        else:
            # 如果不是配对问题，尝试其他常用端口
            print(f"端口 {port} 连接失败，尝试其他常用端口...")
            success, actual_port, output = try_connect_multiple_ports(ip)
            if success:
                port = actual_port
                print(f"✅ 连接成功: {ip}:{port}")
                # 添加设备到历史记录
                add_device_to_history(ip, port, f"Device-{ip}")
            else:
                # 最后尝试配对流程
                print("🔐 尝试配对流程...")
                success, port = try_pairing_then_connect(ip, 5555)
                if not success:
                    print(f"❌ 所有连接方式都失败了")
                    return
                # 配对成功后也添加到历史记录
                add_device_to_history(ip, port, f"Device-{ip}")
    else:
        print(f"✅ 连接成功: {ip}:{port}")
        # 添加设备到历史记录
        add_device_to_history(ip, port, f"Device-{ip}")

    # 查找APK文件
    apk_files = find_apk_files()
    if not apk_files:
        apk_path = input("请输入APK文件路径: ").strip()
        if not apk_path:
            print("APK文件路径不能为空")
            return
    elif len(apk_files) == 1:
        apk_path = apk_files[0]
        print(f"自动选择APK: {apk_path}")
    else:
        print("找到多个APK文件:")
        for i, apk in enumerate(apk_files):
            print(f"  {i+1}. {apk}")

        while True:
            try:
                choice = int(input("选择APK编号: ")) - 1
                if 0 <= choice < len(apk_files):
                    apk_path = apk_files[choice]
                    break
                else:
                    print("无效选择")
            except ValueError:
                print("请输入数字")

    # 安装APK
    print(f"安装 {apk_path}...")
    device_target = f"{ip}:{port}"
    success, output = install_apk(apk_path, device_target)

    if success and "Success" in output:
        print("安装成功!")
    else:
        print(f"安装失败: {output}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n用户取消操作")
    except Exception as e:
        print(f"发生错误: {e}")

package ai.aiot.service

import android.content.Context
import ai.aiot.model.AIConfig
import ai.aiot.model.AIProvider
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.Json
import java.io.File

/**
 * AI配置管理器
 * 负责AI配置的保存、加载和管理
 */
class AIConfigManager(private val context: Context) {
    
    private val json = Json {
        ignoreUnknownKeys = true
        encodeDefaults = true
        prettyPrint = true
    }
    
    private val configFile = File(context.filesDir, "ai_config.json")
    
    /**
     * 保存AI配置
     */
    suspend fun saveConfig(config: AIConfig) = withContext(Dispatchers.IO) {
        try {
            val jsonString = json.encodeToString(AIConfig.serializer(), config)
            configFile.writeText(jsonString)
        } catch (e: Exception) {
            throw Exception("保存AI配置失败: ${e.message}")
        }
    }
    
    /**
     * 加载AI配置
     */
    suspend fun loadConfig(): AIConfig? = withContext(Dispatchers.IO) {
        try {
            if (!configFile.exists()) {
                return@withContext null
            }
            
            val jsonString = configFile.readText()
            json.decodeFromString(AIConfig.serializer(), jsonString)
        } catch (e: Exception) {
            // 如果配置文件损坏，返回null
            null
        }
    }
    
    /**
     * 获取默认配置
     */
    fun getDefaultConfig(): AIConfig {
        return AIConfig(
            provider = AIProvider.OPENAI,
            apiKey = "",
            apiUrl = "https://api.openai.com/v1/chat/completions",
            model = "gpt-3.5-turbo",
            maxTokens = 2000,
            temperature = 0.7f,
            timeout = 30000L
        )
    }
    
    /**
     * 获取或创建配置
     */
    suspend fun getOrCreateConfig(): AIConfig {
        return loadConfig() ?: getDefaultConfig()
    }
    
    /**
     * 删除配置文件
     */
    suspend fun deleteConfig() = withContext(Dispatchers.IO) {
        if (configFile.exists()) {
            configFile.delete()
        }
    }
    
    /**
     * 验证配置是否有效
     */
    fun validateConfig(config: AIConfig): List<String> {
        val errors = mutableListOf<String>()
        
        if (config.apiKey.isBlank()) {
            errors.add("API密钥不能为空")
        }
        
        if (config.apiUrl.isBlank()) {
            errors.add("API地址不能为空")
        }
        
        if (config.model.isBlank()) {
            errors.add("模型名称不能为空")
        }
        
        if (config.maxTokens <= 0) {
            errors.add("最大Token数必须大于0")
        }
        
        if (config.temperature < 0 || config.temperature > 2) {
            errors.add("温度值必须在0-2之间")
        }
        
        if (config.timeout <= 0) {
            errors.add("超时时间必须大于0")
        }
        
        return errors
    }
    
    /**
     * 获取提供商的默认配置
     */
    fun getProviderDefaults(provider: AIProvider): AIConfig {
        return when (provider) {
            AIProvider.OPENAI -> AIConfig(
                provider = provider,
                apiUrl = "https://api.openai.com/v1/chat/completions",
                model = "gpt-3.5-turbo",
                maxTokens = 2000,
                temperature = 0.7f
            )
            AIProvider.CLAUDE -> AIConfig(
                provider = provider,
                apiUrl = "https://api.anthropic.com/v1/messages",
                model = "claude-3-sonnet-20240229",
                maxTokens = 2000,
                temperature = 0.7f
            )
            AIProvider.GEMINI -> AIConfig(
                provider = provider,
                apiUrl = "https://generativelanguage.googleapis.com/v1beta/models",
                model = "gemini-pro",
                maxTokens = 2000,
                temperature = 0.7f
            )
            AIProvider.DEEPSEEK -> AIConfig(
                provider = provider,
                apiUrl = "https://api.deepseek.com/v1/chat/completions",
                model = "deepseek-chat",
                maxTokens = 2000,
                temperature = 0.7f
            )
            AIProvider.CUSTOM -> AIConfig(
                provider = provider,
                apiUrl = "",
                model = "",
                maxTokens = 2000,
                temperature = 0.7f
            )
        }
    }
    
    /**
     * 更新配置的提供商
     */
    fun updateProvider(config: AIConfig, newProvider: AIProvider): AIConfig {
        val defaults = getProviderDefaults(newProvider)
        return config.copy(
            provider = newProvider,
            apiUrl = defaults.apiUrl,
            model = defaults.model
        )
    }
}

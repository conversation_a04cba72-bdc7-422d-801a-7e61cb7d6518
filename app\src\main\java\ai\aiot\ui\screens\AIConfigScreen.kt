package ai.aiot.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import ai.aiot.model.AIConfig
import ai.aiot.model.AIProvider
import ai.aiot.service.AIService
import ai.aiot.service.AIConfigManager
import kotlinx.coroutines.launch

/**
 * AI配置屏幕
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AIConfigScreen(
    config: AIConfig,
    onConfigChanged: (AIConfig) -> Unit,
    onBack: () -> Unit
) {
    val context = LocalContext.current
    val configManager = remember { AIConfigManager(context) }

    var currentConfig by remember { mutableStateOf(config) }
    var showApiKey by remember { mutableStateOf(false) }
    var isTestingConnection by remember { mutableStateOf(false) }
    var testResult by remember { mutableStateOf<String?>(null) }
    var testError by remember { mutableStateOf<String?>(null) }

    val scope = rememberCoroutineScope()
    val scrollState = rememberScrollState()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(scrollState)
    ) {
        // 标题栏
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onBack) {
                Icon(
                    imageVector = Icons.Default.ArrowBack,
                    contentDescription = "返回"
                )
            }
            
            Text(
                text = "AI配置",
                style = MaterialTheme.typography.headlineMedium,
                modifier = Modifier.weight(1f)
            )
            
            // 保存按钮
            Button(
                onClick = {
                    scope.launch {
                        try {
                            configManager.saveConfig(currentConfig)
                            onConfigChanged(currentConfig)
                        } catch (e: Exception) {
                            testError = e.message
                        }
                    }
                },
                enabled = currentConfig.isValid()
            ) {
                Icon(
                    imageVector = Icons.Default.Save,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("保存")
            }
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // AI服务提供商选择
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Text(
                    text = "AI服务提供商",
                    style = MaterialTheme.typography.titleMedium
                )
                
                AIProvider.values().forEach { provider ->
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = currentConfig.provider == provider,
                            onClick = {
                                currentConfig = configManager.updateProvider(currentConfig, provider)
                            }
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Column {
                            Text(
                                text = provider.displayName,
                                style = MaterialTheme.typography.bodyMedium
                            )
                            Text(
                                text = provider.description,
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // API配置
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Text(
                    text = "API配置",
                    style = MaterialTheme.typography.titleMedium
                )
                
                // API密钥
                OutlinedTextField(
                    value = currentConfig.apiKey,
                    onValueChange = { currentConfig = currentConfig.copy(apiKey = it) },
                    label = { Text("API密钥") },
                    placeholder = { Text("输入您的API密钥") },
                    modifier = Modifier.fillMaxWidth(),
                    visualTransformation = if (showApiKey) VisualTransformation.None else PasswordVisualTransformation(),
                    trailingIcon = {
                        IconButton(onClick = { showApiKey = !showApiKey }) {
                            Icon(
                                imageVector = if (showApiKey) Icons.Default.VisibilityOff else Icons.Default.Visibility,
                                contentDescription = if (showApiKey) "隐藏密钥" else "显示密钥"
                            )
                        }
                    },
                    keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next)
                )
                
                // API URL
                OutlinedTextField(
                    value = currentConfig.apiUrl,
                    onValueChange = { currentConfig = currentConfig.copy(apiUrl = it) },
                    label = { Text("API URL") },
                    placeholder = { Text("API服务地址") },
                    modifier = Modifier.fillMaxWidth(),
                    keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next)
                )
                
                // 模型选择
                if (currentConfig.provider.supportedModels.isNotEmpty()) {
                    var expanded by remember { mutableStateOf(false) }
                    
                    ExposedDropdownMenuBox(
                        expanded = expanded,
                        onExpandedChange = { expanded = !expanded }
                    ) {
                        OutlinedTextField(
                            value = currentConfig.model,
                            onValueChange = { },
                            readOnly = true,
                            label = { Text("模型") },
                            trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded) },
                            modifier = Modifier
                                .fillMaxWidth()
                                .menuAnchor()
                        )
                        
                        ExposedDropdownMenu(
                            expanded = expanded,
                            onDismissRequest = { expanded = false }
                        ) {
                            currentConfig.provider.supportedModels.forEach { model ->
                                DropdownMenuItem(
                                    text = { Text(model) },
                                    onClick = {
                                        currentConfig = currentConfig.copy(model = model)
                                        expanded = false
                                    }
                                )
                            }
                        }
                    }
                } else {
                    OutlinedTextField(
                        value = currentConfig.model,
                        onValueChange = { currentConfig = currentConfig.copy(model = it) },
                        label = { Text("模型名称") },
                        placeholder = { Text("输入模型名称") },
                        modifier = Modifier.fillMaxWidth(),
                        keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next)
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 高级设置
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Text(
                    text = "高级设置",
                    style = MaterialTheme.typography.titleMedium
                )
                
                // 最大Token数
                OutlinedTextField(
                    value = currentConfig.maxTokens.toString(),
                    onValueChange = { 
                        it.toIntOrNull()?.let { tokens ->
                            currentConfig = currentConfig.copy(maxTokens = tokens)
                        }
                    },
                    label = { Text("最大Token数") },
                    modifier = Modifier.fillMaxWidth(),
                    keyboardOptions = KeyboardOptions(
                        keyboardType = KeyboardType.Number,
                        imeAction = ImeAction.Next
                    )
                )
                
                // 温度参数
                Column {
                    Text(
                        text = "温度参数: ${String.format("%.1f", currentConfig.temperature)}",
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Slider(
                        value = currentConfig.temperature,
                        onValueChange = { currentConfig = currentConfig.copy(temperature = it) },
                        valueRange = 0f..2f,
                        steps = 19
                    )
                    Text(
                        text = "较低值使输出更确定，较高值使输出更随机",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                // 超时设置
                OutlinedTextField(
                    value = (currentConfig.timeout / 1000).toString(),
                    onValueChange = { 
                        it.toLongOrNull()?.let { seconds ->
                            currentConfig = currentConfig.copy(timeout = seconds * 1000)
                        }
                    },
                    label = { Text("超时时间（秒）") },
                    modifier = Modifier.fillMaxWidth(),
                    keyboardOptions = KeyboardOptions(
                        keyboardType = KeyboardType.Number,
                        imeAction = ImeAction.Done
                    )
                )
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 连接测试
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Text(
                    text = "连接测试",
                    style = MaterialTheme.typography.titleMedium
                )
                
                Button(
                    onClick = {
                        scope.launch {
                            isTestingConnection = true
                            testResult = null
                            testError = null
                            
                            try {
                                val aiService = AIService()
                                val result = aiService.testConnection(currentConfig)

                                if (result.isSuccess) {
                                    testResult = result.getOrNull()
                                } else {
                                    testError = result.exceptionOrNull()?.message
                                }
                            } catch (e: Exception) {
                                testError = e.message
                            } finally {
                                isTestingConnection = false
                            }
                        }
                    },
                    enabled = !isTestingConnection && currentConfig.isValid(),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    if (isTestingConnection) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            strokeWidth = 2.dp
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("测试中...")
                    } else {
                        Icon(
                            imageVector = Icons.Default.NetworkCheck,
                            contentDescription = null,
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("测试连接")
                    }
                }
                
                // 测试结果
                val currentTestResult = testResult
                if (currentTestResult != null) {
                    Card(
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.primaryContainer
                        )
                    ) {
                        Row(
                            modifier = Modifier.padding(12.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.CheckCircle,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.onPrimaryContainer
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = currentTestResult,
                                color = MaterialTheme.colorScheme.onPrimaryContainer
                            )
                        }
                    }
                }

                val currentTestError = testError
                if (currentTestError != null) {
                    Card(
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.errorContainer
                        )
                    ) {
                        Row(
                            modifier = Modifier.padding(12.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.Error,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.onErrorContainer
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "连接失败: $currentTestError",
                                color = MaterialTheme.colorScheme.onErrorContainer
                            )
                        }
                    }
                }
            }
        }
        
        Spacer(modifier = Modifier.height(32.dp))
    }
}

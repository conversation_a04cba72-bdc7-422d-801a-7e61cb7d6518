# 设备记录功能说明

## 🎉 新增功能概述

`quick_install.py` 现在支持记录和管理无线连接的手机设备，可以自动尝试连接之前成功连接过的设备，大大简化重复连接的操作。

## 📱 主要功能

### 1. 自动设备记录
- **自动保存**: 每次成功连接设备后，自动保存设备信息
- **设备信息**: 记录IP地址、端口、设备名称、连接时间和连接次数
- **持久化存储**: 设备信息保存在用户目录的 `.adb_device_history.json` 文件中

### 2. 智能连接尝试
- **优先连接**: 启动时自动尝试连接最近使用的设备
- **按时间排序**: 优先尝试最近连接过的设备
- **失败回退**: 如果历史设备连接失败，回退到手动输入模式

### 3. 历史管理
- **查看历史**: 使用 `--history` 参数查看所有历史设备
- **清除历史**: 使用 `--clear-history` 参数清除所有历史记录
- **连接统计**: 显示每个设备的连接次数和最后连接时间

## 🚀 使用方式

### 基本使用（推荐）
```bash
# 直接运行，会自动尝试连接历史设备
python quick_install.py

# 指定APK文件和设备IP（首次连接）
python quick_install.py --ip ************* --apk app-debug.apk
```

### 历史管理
```bash
# 查看设备连接历史
python quick_install.py --history

# 清除设备连接历史
python quick_install.py --clear-history
```

### 其他功能
```bash
# 仅配对设备（不安装APK）
python quick_install.py --pair-only --ip *************

# 列出当前连接的设备
python quick_install.py --list-devices
```

## 📋 工作流程

### 首次连接设备
1. 运行 `python quick_install.py --ip <设备IP> --apk <APK文件>`
2. 脚本尝试连接设备（可能需要配对）
3. 连接成功后，设备信息自动保存到历史记录
4. 安装APK到设备

### 后续连接（自动模式）
1. 运行 `python quick_install.py`
2. 脚本自动尝试连接历史设备（按最近连接时间排序）
3. 如果连接成功，直接进入APK选择和安装流程
4. 如果连接失败，提供历史设备选择或手动输入选项

### 交互式历史选择
当没有USB设备连接时，脚本会：
1. 显示历史连接设备列表
2. 用户可以选择尝试连接特定的历史设备
3. 或者跳过历史设备，进行手动配置

## 📊 历史记录格式

设备历史记录保存在 `~/.adb_device_history.json`，格式如下：

```json
{
  "*************:5555": {
    "ip": "*************",
    "port": 5555,
    "device_name": "Device-*************",
    "last_connected": "2024-01-15T10:30:45.123456",
    "connection_count": 5
  }
}
```

## 🔧 技术特性

### 智能连接策略
- **多端口尝试**: 自动尝试常用端口（5555, 5556, 5557等）
- **配对支持**: 支持Android 11+的无线调试配对
- **错误恢复**: 连接失败时提供多种恢复选项

### 安全性
- **本地存储**: 设备信息仅保存在本地，不上传到任何服务器
- **权限控制**: 历史文件保存在用户目录，具有适当的访问权限
- **数据验证**: 加载历史记录时进行数据验证和错误处理

### 用户体验
- **进度提示**: 详细的连接进度和状态提示
- **彩色输出**: 使用emoji和颜色区分不同类型的消息
- **智能选择**: 自动选择最佳的连接方式和设备

## 📱 示例场景

### 场景1：开发者日常使用
```bash
# 第一次连接新设备
python quick_install.py --ip ************* --apk myapp.apk
# ✅ 连接成功，设备已保存到历史

# 之后每次使用，只需要：
python quick_install.py
# 🔍 尝试连接最近使用的设备...
# ✅ 成功连接到历史设备: *************:5555
# 🎯 自动选择APK: myapp.apk
# ✅ 安装成功
```

### 场景2：多设备管理
```bash
# 查看所有历史设备
python quick_install.py --history
# 📱 设备连接历史:
#  1. Device-************* (*************:5555) - 2024-01-15 10:30
#  2. Device-************* (*************:5555) - 2024-01-14 15:20

# 连接特定设备
python quick_install.py --ip ************* --apk newapp.apk
```

### 场景3：清理和维护
```bash
# 清除过期的设备记录
python quick_install.py --clear-history
# ✅ 设备连接历史已清除
```

## 🎯 优势

1. **提高效率**: 无需每次手动输入设备IP
2. **减少错误**: 自动使用已验证的设备信息
3. **智能管理**: 按使用频率和时间智能排序设备
4. **无缝体验**: 从历史设备连接到APK安装一气呵成
5. **灵活控制**: 支持手动覆盖和历史管理

---

**🎊 现在您可以享受更加便捷的无线APK安装体验！**

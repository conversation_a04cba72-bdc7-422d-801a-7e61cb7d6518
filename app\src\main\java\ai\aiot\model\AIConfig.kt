package ai.aiot.model

import kotlinx.serialization.Serializable

/**
 * AI配置数据模型
 */
@Serializable
data class AIConfig(
    val provider: AIProvider = AIProvider.OPENAI,
    val apiKey: String = "",
    val apiUrl: String = "",
    val model: String = "",
    val maxTokens: Int = 4000,
    val temperature: Float = 0.7f,
    val timeout: Long = 30000L, // 30秒
    val isEnabled: Boolean = false,
    val customHeaders: Map<String, String> = emptyMap()
) {
    /**
     * 检查配置是否有效
     */
    fun isValid(): Boolean {
        return apiKey.isNotBlank() && 
               apiUrl.isNotBlank() && 
               model.isNotBlank()
    }
    
    /**
     * 获取默认配置
     */
    fun getDefaultConfig(): AIConfig {
        return when (provider) {
            AIProvider.OPENAI -> copy(
                apiUrl = "https://api.openai.com/v1/chat/completions",
                model = "gpt-3.5-turbo"
            )
            AIProvider.CLAUDE -> copy(
                apiUrl = "https://api.anthropic.com/v1/messages",
                model = "claude-3-sonnet-20240229"
            )
            AIProvider.GEMINI -> copy(
                apiUrl = "https://generativelanguage.googleapis.com/v1beta/models",
                model = "gemini-pro"
            )
            AIProvider.CUSTOM -> this
        }
    }
}

/**
 * AI服务提供商
 */
@Serializable
enum class AIProvider(
    val displayName: String,
    val description: String,
    val defaultModel: String,
    val supportedModels: List<String>
) {
    OPENAI(
        displayName = "OpenAI",
        description = "OpenAI GPT系列模型",
        defaultModel = "gpt-3.5-turbo",
        supportedModels = listOf(
            "gpt-3.5-turbo",
            "gpt-3.5-turbo-16k",
            "gpt-4",
            "gpt-4-turbo-preview",
            "gpt-4-vision-preview"
        )
    ),
    
    CLAUDE(
        displayName = "Claude",
        description = "Anthropic Claude系列模型",
        defaultModel = "claude-3-sonnet-20240229",
        supportedModels = listOf(
            "claude-3-haiku-20240307",
            "claude-3-sonnet-20240229",
            "claude-3-opus-20240229"
        )
    ),
    
    GEMINI(
        displayName = "Gemini",
        description = "Google Gemini系列模型",
        defaultModel = "gemini-pro",
        supportedModels = listOf(
            "gemini-pro",
            "gemini-pro-vision"
        )
    ),
    
    CUSTOM(
        displayName = "自定义",
        description = "自定义AI服务",
        defaultModel = "",
        supportedModels = emptyList()
    )
}

/**
 * AI请求数据模型
 */
@Serializable
data class AIRequest(
    val messages: List<AIMessage>,
    val model: String,
    val maxTokens: Int = 4000,
    val temperature: Float = 0.7f,
    val stream: Boolean = false
)

/**
 * AI消息数据模型
 */
@Serializable
data class AIMessage(
    val role: String, // "system", "user", "assistant"
    val content: String
)

/**
 * AI响应数据模型
 */
@Serializable
data class AIResponse(
    val id: String = "",
    val choices: List<AIChoice> = emptyList(),
    val usage: AIUsage? = null,
    val error: AIError? = null
) {
    fun getContent(): String {
        return choices.firstOrNull()?.message?.content ?: ""
    }
    
    fun isSuccess(): Boolean {
        return error == null && choices.isNotEmpty()
    }
}

/**
 * AI选择数据模型
 */
@Serializable
data class AIChoice(
    val index: Int = 0,
    val message: AIMessage,
    val finishReason: String? = null
)

/**
 * AI使用统计
 */
@Serializable
data class AIUsage(
    val promptTokens: Int = 0,
    val completionTokens: Int = 0,
    val totalTokens: Int = 0
)

/**
 * AI错误信息
 */
@Serializable
data class AIError(
    val message: String,
    val type: String? = null,
    val code: String? = null
)

/**
 * 代码编辑请求
 */
@Serializable
data class CodeEditRequest(
    val filePath: String,
    val fileName: String,
    val language: String,
    val content: String,
    val lineRange: String, // 16进制行号范围，如 [0x64-0xC8]
    val instruction: String,
    val context: String = "" // 额外上下文信息
) {
    /**
     * 生成AI提示词
     */
    fun generatePrompt(): String {
        return buildString {
            appendLine("你是一个专业的代码编辑助手。请根据以下信息修改代码：")
            appendLine()
            appendLine("文件信息：")
            appendLine("- 文件名：$fileName")
            appendLine("- 语言：$language")
            appendLine("- 行号范围：$lineRange")
            appendLine()
            appendLine("修改指令：")
            appendLine(instruction)
            appendLine()
            if (context.isNotBlank()) {
                appendLine("上下文信息：")
                appendLine(context)
                appendLine()
            }
            appendLine("原始代码：")
            appendLine("```$language")
            appendLine(content)
            appendLine("```")
            appendLine()
            appendLine("请提供修改后的代码，并说明修改原因。格式如下：")
            appendLine("```$language")
            appendLine("// 修改后的代码")
            appendLine("```")
            appendLine()
            appendLine("修改说明：")
            appendLine("// 说明修改的原因和改进点")
        }
    }
}

/**
 * 代码编辑响应
 */
@Serializable
data class CodeEditResponse(
    val originalCode: String,
    val modifiedCode: String,
    val explanation: String,
    val lineRange: String,
    val isApproved: Boolean = false,
    val timestamp: Long = System.currentTimeMillis()
) {
    /**
     * 提取代码块
     */
    fun extractCodeBlock(response: String, language: String): String {
        val codeBlockRegex = "```$language\\s*\\n([\\s\\S]*?)\\n```".toRegex()
        val match = codeBlockRegex.find(response)
        return match?.groupValues?.get(1)?.trim() ?: response
    }
    
    /**
     * 提取说明文本
     */
    fun extractExplanation(response: String): String {
        val explanationRegex = "修改说明：\\s*\\n([\\s\\S]*)".toRegex()
        val match = explanationRegex.find(response)
        return match?.groupValues?.get(1)?.trim() ?: ""
    }
}

# AI编程IDE - Android应用

一个功能完整的AI编程IDE Android应用，包含文件管理、代码编辑和AI聊天功能。

## 🚀 快速开始

### 在Android Studio中打开项目

1. **打开Android Studio**
2. **选择 "Open an existing project"**
3. **导航到项目目录并选择**
4. **等待Gradle同步完成**

### 解决SDK版本警告

如果遇到以下警告：
```
Warning: SDK processing. This version only understands SDK XML versions up to 3 but an SDK XML file of version 4 was encountered.
```

请按照以下步骤解决：

#### 方法1：在Android Studio中更新（推荐）

1. **打开 Tools > SDK Manager**
2. **在 "SDK Platforms" 标签页中：**
   - 确保安装了 Android 14 (API 34) 和 Android 15 (API 35)
   - 如果没有，请勾选并点击 "Apply"

3. **在 "SDK Tools" 标签页中：**
   - 确保以下工具是最新版本：
     - Android SDK Build-Tools
     - Android SDK Command-line Tools
     - Android SDK Platform-Tools
   - 如果有更新，请勾选并点击 "Apply"

4. **重启Android Studio**

#### 方法2：清理和重建项目

1. **Build > Clean Project**
2. **Build > Rebuild Project**
3. **File > Invalidate Caches and Restart**

#### 方法3：检查Gradle配置

确保以下文件中的版本是最新的：

**gradle/libs.versions.toml:**
```toml
[versions]
agp = "8.7.2"
kotlin = "2.0.21"
compose-compiler = "1.5.15"
```

**app/build.gradle.kts:**
```kotlin
android {
    compileSdk = 35
    
    defaultConfig {
        targetSdk = 35
    }
}
```

## 📱 应用功能

### 🗂️ 文件管理系统
- 文件树浏览
- 创建/删除/重命名文件和文件夹
- 文件类型识别和图标显示
- 文件大小显示

### 💻 代码编辑器
- 语法高亮支持
- 行号显示
- 撤销/重做功能
- 文件保存
- 多种编程语言支持

### 🤖 AI聊天助手
- 智能对话界面
- 代码生成和解释
- 编程问题解答
- 代码片段展示

### 🎨 用户界面
- Material Design 3风格
- 响应式布局
- 深色/浅色主题支持
- 直观的导航系统

## 🏗️ 项目结构

```
app/src/main/java/ai/aiot/
├── model/                  # 数据模型
│   ├── FileItem.kt
│   ├── ChatMessage.kt
│   └── EditorState.kt
├── viewmodel/             # 视图模型
│   ├── FileManagerViewModel.kt
│   ├── CodeEditorViewModel.kt
│   └── ChatViewModel.kt
├── ui/
│   ├── components/        # UI组件
│   │   ├── FileManagerPanel.kt
│   │   ├── CodeEditorPanel.kt
│   │   ├── ChatPanel.kt
│   │   └── CreateItemDialog.kt
│   └── screens/          # 屏幕
│       ├── IDEMainScreen.kt
│       └── PermissionScreen.kt
├── utils/                # 工具类
│   ├── PermissionUtils.kt
│   └── SampleProjectCreator.kt
└── MainActivity.kt       # 主活动
```

## 🔧 技术栈

- **Kotlin** - 主要编程语言
- **Jetpack Compose** - 现代UI框架
- **Material Design 3** - 设计系统
- **MVVM架构** - 架构模式
- **Coroutines** - 异步编程
- **Navigation Compose** - 导航
- **ViewModel** - 状态管理

## 📋 系统要求

- **Android 10 (API 29)** 或更高版本
- **存储权限** - 用于文件管理
- **网络权限** - 用于AI功能（未来扩展）

## 🚀 运行应用

1. **连接Android设备或启动模拟器**
2. **在Android Studio中点击运行按钮**
3. **首次启动时授予文件访问权限**
4. **开始使用AI编程IDE！**

## 🔮 未来功能

- [ ] 真实AI服务集成
- [ ] 更多编程语言支持
- [ ] 项目模板系统
- [ ] Git版本控制
- [ ] 插件系统
- [ ] 云同步功能
- [ ] 代码调试器
- [ ] 智能代码补全

## 🐛 故障排除

### 构建失败
1. 确保使用最新版本的Android Studio
2. 检查网络连接（下载依赖项）
3. 清理项目缓存
4. 重新同步Gradle

### 权限问题
1. 在设备设置中手动授予存储权限
2. 对于Android 11+，需要"管理所有文件"权限

### 性能问题
1. 确保设备有足够的存储空间
2. 关闭其他占用内存的应用
3. 使用性能较好的设备或模拟器

## 📞 支持

如果遇到问题，请检查：
1. Android Studio版本是否为最新
2. SDK工具是否已更新
3. 设备是否满足最低系统要求

---

**享受使用AI编程IDE！** 🎉

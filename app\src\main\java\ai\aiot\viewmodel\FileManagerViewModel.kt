package ai.aiot.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import ai.aiot.model.FileItem
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.io.File

/**
 * 文件管理器ViewModel
 */
class FileManagerViewModel : ViewModel() {
    
    private val _fileTree = MutableStateFlow<List<FileItem>>(emptyList())
    val fileTree: StateFlow<List<FileItem>> = _fileTree.asStateFlow()
    
    private val _currentPath = MutableStateFlow("")
    val currentPath: StateFlow<String> = _currentPath.asStateFlow()
    
    private val _selectedFile = MutableStateFlow<FileItem?>(null)
    val selectedFile: StateFlow<FileItem?> = _selectedFile.asStateFlow()
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()
    
    /**
     * 加载指定路径的文件
     */
    fun loadFiles(path: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _errorMessage.value = null
                
                val directory = File(path)
                if (!directory.exists() || !directory.isDirectory) {
                    _errorMessage.value = "目录不存在或不是有效目录"
                    return@launch
                }
                
                val files = directory.listFiles()?.map { file ->
                    FileItem.fromFile(file).apply {
                        if (isDirectory) {
                            // 预加载子目录的第一层
                            loadChildren(this)
                        }
                    }
                }?.sortedWith(compareBy<FileItem> { !it.isDirectory }.thenBy { it.name }) ?: emptyList()
                
                _fileTree.value = files
                _currentPath.value = path
                
            } catch (e: Exception) {
                _errorMessage.value = "加载文件失败: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 展开或折叠文件夹
     */
    fun toggleFolder(fileItem: FileItem) {
        if (!fileItem.isDirectory) return
        
        viewModelScope.launch {
            try {
                fileItem.isExpanded = !fileItem.isExpanded
                
                if (fileItem.isExpanded && fileItem.children.isEmpty()) {
                    loadChildren(fileItem)
                }
                
                // 更新UI
                _fileTree.value = _fileTree.value.toList()
                
            } catch (e: Exception) {
                _errorMessage.value = "展开文件夹失败: ${e.message}"
            }
        }
    }
    
    /**
     * 选择文件
     */
    fun selectFile(fileItem: FileItem) {
        _selectedFile.value = fileItem
    }
    
    /**
     * 创建新文件
     */
    fun createFile(parentPath: String, fileName: String): Boolean {
        return try {
            val parentDir = File(parentPath)
            if (!parentDir.exists()) {
                _errorMessage.value = "父目录不存在: $parentPath"
                return false
            }

            if (!parentDir.canWrite()) {
                _errorMessage.value = "没有写入权限: $parentPath"
                return false
            }

            val newFile = File(parentDir, fileName)
            if (newFile.exists()) {
                _errorMessage.value = "文件已存在: $fileName"
                false
            } else {
                // 确保父目录存在
                parentDir.mkdirs()

                // 创建文件
                val created = newFile.createNewFile()
                if (created) {
                    refreshCurrentDirectory()
                    _errorMessage.value = null // 清除之前的错误
                    true
                } else {
                    _errorMessage.value = "创建文件失败，可能是权限问题"
                    false
                }
            }
        } catch (e: SecurityException) {
            _errorMessage.value = "权限不足，无法创建文件: ${e.message}"
            false
        } catch (e: Exception) {
            _errorMessage.value = "创建文件失败: ${e.message}"
            false
        }
    }
    
    /**
     * 创建新文件夹
     */
    fun createFolder(parentPath: String, folderName: String): Boolean {
        return try {
            val parentDir = File(parentPath)
            if (!parentDir.exists()) {
                _errorMessage.value = "父目录不存在: $parentPath"
                return false
            }

            if (!parentDir.canWrite()) {
                _errorMessage.value = "没有写入权限: $parentPath"
                return false
            }

            val newFolder = File(parentDir, folderName)
            if (newFolder.exists()) {
                _errorMessage.value = "文件夹已存在: $folderName"
                false
            } else {
                val created = newFolder.mkdirs()
                if (created) {
                    refreshCurrentDirectory()
                    _errorMessage.value = null // 清除之前的错误
                    true
                } else {
                    _errorMessage.value = "创建文件夹失败，可能是权限问题"
                    false
                }
            }
        } catch (e: SecurityException) {
            _errorMessage.value = "权限不足，无法创建文件夹: ${e.message}"
            false
        } catch (e: Exception) {
            _errorMessage.value = "创建文件夹失败: ${e.message}"
            false
        }
    }
    
    /**
     * 删除文件或文件夹
     */
    fun deleteFile(fileItem: FileItem): Boolean {
        return try {
            val file = File(fileItem.path)
            val deleted = if (file.isDirectory) {
                file.deleteRecursively()
            } else {
                file.delete()
            }
            
            if (deleted) {
                refreshCurrentDirectory()
                if (_selectedFile.value?.path == fileItem.path) {
                    _selectedFile.value = null
                }
            } else {
                _errorMessage.value = "删除失败"
            }
            
            deleted
        } catch (e: Exception) {
            _errorMessage.value = "删除失败: ${e.message}"
            false
        }
    }
    
    /**
     * 重命名文件或文件夹
     */
    fun renameFile(fileItem: FileItem, newName: String): Boolean {
        return try {
            val oldFile = File(fileItem.path)
            val newFile = File(oldFile.parent, newName)
            
            if (newFile.exists()) {
                _errorMessage.value = "目标名称已存在"
                false
            } else {
                val renamed = oldFile.renameTo(newFile)
                if (renamed) {
                    refreshCurrentDirectory()
                    if (_selectedFile.value?.path == fileItem.path) {
                        _selectedFile.value = _selectedFile.value?.copy(
                            name = newName,
                            path = newFile.absolutePath
                        )
                    }
                } else {
                    _errorMessage.value = "重命名失败"
                }
                renamed
            }
        } catch (e: Exception) {
            _errorMessage.value = "重命名失败: ${e.message}"
            false
        }
    }
    
    /**
     * 加载子文件
     */
    private fun loadChildren(parent: FileItem) {
        try {
            val directory = File(parent.path)
            val children = directory.listFiles()?.map { file ->
                FileItem.fromFile(file)
            }?.sortedWith(compareBy<FileItem> { !it.isDirectory }.thenBy { it.name }) ?: emptyList()
            
            parent.children.clear()
            parent.children.addAll(children)
        } catch (e: Exception) {
            _errorMessage.value = "加载子文件失败: ${e.message}"
        }
    }
    
    /**
     * 刷新当前目录
     */
    private fun refreshCurrentDirectory() {
        val currentPath = _currentPath.value
        if (currentPath.isNotEmpty()) {
            loadFiles(currentPath)
        }
    }
    
    /**
     * 设置错误消息
     */
    fun setError(message: String) {
        _errorMessage.value = message
    }

    /**
     * 清除错误消息
     */
    fun clearError() {
        _errorMessage.value = null
    }
}

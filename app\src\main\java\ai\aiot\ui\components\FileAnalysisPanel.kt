package ai.aiot.ui.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import ai.aiot.model.FileHierarchy
import ai.aiot.model.HierarchyNode
import ai.aiot.model.NodeType

/**
 * 文件分析面板
 */
@Composable
fun FileAnalysisPanel(
    isLoading: Boolean,
    loadingProgress: Float,
    loadingMessage: String,
    fileHierarchy: FileHierarchy?,
    onAnalysisComplete: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 标题
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Analytics,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "文件结构分析",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            when {
                isLoading -> {
                    LoadingAnalysis(
                        progress = loadingProgress,
                        message = loadingMessage
                    )
                }
                
                fileHierarchy != null -> {
                    CompletedAnalysis(
                        hierarchy = fileHierarchy,
                        onAnalysisComplete = onAnalysisComplete
                    )
                }
                
                else -> {
                    EmptyAnalysis()
                }
            }
        }
    }
}

/**
 * 加载中的分析界面
 */
@Composable
private fun LoadingAnalysis(
    progress: Float,
    message: String
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        // 动画图标
        val infiniteTransition = rememberInfiniteTransition(label = "loading")
        val rotation by infiniteTransition.animateFloat(
            initialValue = 0f,
            targetValue = 360f,
            animationSpec = infiniteRepeatable(
                animation = tween(2000, easing = LinearEasing),
                repeatMode = RepeatMode.Restart
            ),
            label = "rotation"
        )
        
        Box(
            modifier = Modifier.size(48.dp),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = Icons.Default.Refresh,
                contentDescription = null,
                modifier = Modifier
                    .size(32.dp)
                    .graphicsLayer { rotationZ = rotation },
                tint = MaterialTheme.colorScheme.primary
            )
        }
        
        // 进度条
        if (progress > 0f) {
            LinearProgressIndicator(
                progress = progress,
                modifier = Modifier.fillMaxWidth(),
                color = MaterialTheme.colorScheme.primary
            )
        } else {
            LinearProgressIndicator(
                modifier = Modifier.fillMaxWidth(),
                color = MaterialTheme.colorScheme.primary
            )
        }
        
        // 加载消息
        Text(
            text = message,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        // 分析步骤
        AnalysisSteps(currentStep = getCurrentStep(message))
    }
}

/**
 * 完成的分析界面
 */
@Composable
private fun CompletedAnalysis(
    hierarchy: FileHierarchy,
    onAnalysisComplete: (String) -> Unit
) {
    LaunchedEffect(hierarchy) {
        onAnalysisComplete(hierarchy.hierarchyString)
    }
    
    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        // 成功图标
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.CheckCircle,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "分析完成",
                style = MaterialTheme.typography.titleSmall,
                color = MaterialTheme.colorScheme.primary,
                fontWeight = FontWeight.Bold
            )
        }
        
        // 统计信息
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            StatisticCard(
                icon = Icons.Default.InsertDriveFile,
                count = hierarchy.totalFiles,
                label = "文件"
            )
            
            StatisticCard(
                icon = Icons.Default.Folder,
                count = hierarchy.totalFolders,
                label = "文件夹"
            )
            
            StatisticCard(
                icon = Icons.Default.Schedule,
                count = ((System.currentTimeMillis() - hierarchy.generatedTime) / 1000).toInt(),
                label = "秒前"
            )
        }
        
        // 层次结构预览
        HierarchyPreview(hierarchy = hierarchy)
        
        // AI提示
        Card(
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.primaryContainer
            )
        ) {
            Row(
                modifier = Modifier.padding(12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.SmartToy,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onPrimaryContainer
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "AI助手已获得项目文件结构访问权限，可以帮助您进行代码分析和编辑。",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
            }
        }
    }
}

/**
 * 空分析界面
 */
@Composable
private fun EmptyAnalysis() {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(24.dp),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Icon(
                imageVector = Icons.Default.FolderOpen,
                contentDescription = null,
                modifier = Modifier.size(32.dp),
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Text(
                text = "等待项目加载",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Text(
                text = "选择项目后开始分析文件结构",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * 分析步骤指示器
 */
@Composable
private fun AnalysisSteps(currentStep: Int) {
    val steps = listOf(
        "扫描文件夹",
        "分析文件类型",
        "构建层次结构",
        "生成索引",
        "完成分析"
    )
    
    LazyColumn(
        modifier = Modifier.heightIn(max = 120.dp),
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        items(steps.size) { index ->
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                val isCompleted = index < currentStep
                val isCurrent = index == currentStep
                
                Icon(
                    imageVector = when {
                        isCompleted -> Icons.Default.CheckCircle
                        isCurrent -> Icons.Default.RadioButtonChecked
                        else -> Icons.Default.RadioButtonUnchecked
                    },
                    contentDescription = null,
                    modifier = Modifier.size(16.dp),
                    tint = when {
                        isCompleted -> MaterialTheme.colorScheme.primary
                        isCurrent -> MaterialTheme.colorScheme.secondary
                        else -> MaterialTheme.colorScheme.onSurfaceVariant
                    }
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = steps[index],
                    style = MaterialTheme.typography.bodySmall,
                    color = when {
                        isCompleted -> MaterialTheme.colorScheme.primary
                        isCurrent -> MaterialTheme.colorScheme.secondary
                        else -> MaterialTheme.colorScheme.onSurfaceVariant
                    }
                )
            }
        }
    }
}

/**
 * 统计卡片
 */
@Composable
private fun StatisticCard(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    count: Int,
    label: String
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            modifier = Modifier.size(20.dp),
            tint = MaterialTheme.colorScheme.primary
        )
        Text(
            text = count.toString(),
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * 层次结构预览
 */
@Composable
private fun HierarchyPreview(hierarchy: FileHierarchy) {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Text(
                text = "层次结构预览",
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(6.dp))
                    .background(MaterialTheme.colorScheme.surfaceVariant)
                    .padding(8.dp)
            ) {
                Text(
                    text = hierarchy.hierarchyString.take(100) + 
                           if (hierarchy.hierarchyString.length > 100) "..." else "",
                    style = MaterialTheme.typography.bodySmall,
                    fontFamily = FontFamily.Monospace,
                    maxLines = 3,
                    overflow = TextOverflow.Ellipsis
                )
            }
        }
    }
}

/**
 * 根据消息获取当前步骤
 */
private fun getCurrentStep(message: String): Int {
    return when {
        message.contains("扫描") || message.contains("分析文件夹") -> 0
        message.contains("分析") || message.contains("文件类型") -> 1
        message.contains("构建") || message.contains("层次") -> 2
        message.contains("生成") || message.contains("索引") -> 3
        message.contains("完成") -> 4
        else -> 0
    }
}

# 🔧 故障排除指南

## SDK版本警告解决方案

### 问题描述
```
Warning: SDK processing. This version only understands SDK XML versions up to 3 but an SDK XML file of version 4 was encountered.
```

### 🎯 解决方案

#### 方案1：在Android Studio中解决（推荐）

1. **打开Android Studio**
2. **打开项目** (`C:\Users\<USER>\AndroidStudioProjects\aiout`)
3. **更新SDK工具**：
   - 打开 `Tools > SDK Manager`
   - 切换到 `SDK Tools` 标签页
   - 确保以下工具是最新版本：
     - ✅ Android SDK Build-Tools
     - ✅ Android SDK Command-line Tools (latest)
     - ✅ Android SDK Platform-Tools
   - 如有更新，勾选并点击 `Apply`

4. **同步项目**：
   - 点击 `File > Sync Project with Gradle Files`
   - 等待同步完成

#### 方案2：使用修复脚本

1. **运行修复脚本**：
   ```cmd
   fix-build-issues.bat
   ```

2. **在Android Studio中重新打开项目**

#### 方案3：手动清理

1. **删除缓存目录**：
   ```cmd
   rmdir /s /q .gradle
   rmdir /s /q build
   rmdir /s /q app\build
   ```

2. **重新同步Gradle**

### 🚀 验证修复

修复后，您应该能够：

1. ✅ 在Android Studio中正常打开项目
2. ✅ Gradle同步成功
3. ✅ 构建项目无错误
4. ✅ 运行应用到设备/模拟器

## 常见问题

### Q1: Gradle同步失败
**解决方案：**
- 检查网络连接
- 使用VPN（如果在中国大陆）
- 清理Gradle缓存
- 重启Android Studio

### Q2: 编译错误
**解决方案：**
- 确保使用Android Studio Hedgehog或更新版本
- 检查JDK版本（推荐JDK 17）
- 更新Android Gradle Plugin

### Q3: 运行时权限问题
**解决方案：**
- 在设备设置中手动授予存储权限
- 对于Android 11+，需要"管理所有文件"权限
- 重启应用

### Q4: 应用崩溃
**解决方案：**
- 检查Logcat输出
- 确保设备满足最低API要求（API 29）
- 检查权限是否正确授予

## 🔍 调试技巧

### 查看详细错误信息
```cmd
./gradlew assembleDebug --stacktrace --info
```

### 检查依赖冲突
```cmd
./gradlew app:dependencies
```

### 清理并重建
```cmd
./gradlew clean
./gradlew assembleDebug
```

## 📞 获取帮助

如果问题仍然存在：

1. **检查Android Studio版本**：推荐使用最新稳定版
2. **检查系统要求**：确保满足最低要求
3. **查看官方文档**：Android开发者文档
4. **社区支持**：Stack Overflow, Android开发者社区

## 🎯 成功指标

修复成功后，您应该看到：

- ✅ Android Studio中项目正常加载
- ✅ 无Gradle同步错误
- ✅ 可以成功构建APK
- ✅ 应用可以正常安装和运行
- ✅ 所有功能正常工作：
  - 文件管理器
  - 代码编辑器
  - AI聊天界面

---

**记住：大多数构建问题都可以通过清理缓存和更新工具来解决！** 🚀

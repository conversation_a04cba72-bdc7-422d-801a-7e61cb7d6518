ai.aiot.MainActivity"ai.aiot.model.AIConfig.$serializerai.aiot.model.AIProvider#ai.aiot.model.AIRequest.$serializer#ai.aiot.model.AIMessage.$serializer$ai.aiot.model.AIResponse.$serializer"ai.aiot.model.AIChoice.$serializer!ai.aiot.model.AIUsage.$serializer!ai.aiot.model.AIError.$serializer)ai.aiot.model.CodeEditRequest.$serializer*ai.aiot.model.CodeEditResponse.$serializer%ai.aiot.model.ChatMessage.$serializerai.aiot.model.MessageType%ai.aiot.model.CodeSnippet.$serializerai.aiot.model.EditorAction"ai.aiot.model.FileItem.$serializerai.aiot.model.FileType!ai.aiot.model.Project.$serializer'ai.aiot.model.ProjectFolder.$serializer'ai.aiot.model.FileHierarchy.$serializerai.aiot.model.NodeType*ai.aiot.model.AIEditPermission.$serializer ai.aiot.ui.components.CreateTypeai.aiot.ui.screens.IDEPanelai.aiot.viewmodel.ChatViewModel%ai.aiot.viewmodel.CodeEditorViewModel&ai.aiot.viewmodel.FileManagerViewModel)ai.aiot.viewmodel.ProjectManagerViewModelai.aiot.model.AIOperationType,ai.aiot.model.AIOperationRequest.$serializer-ai.aiot.model.AIOperationResponse.$serializer-ai.aiot.model.ConversationHistory.$serializer"ai.aiot.viewmodel.ViewModelFactory>ai.aiot.service.FileHistoryManager.FileHistoryItem.$serializer:ai.aiot.service.FileHistoryManager.FolderState.$serializer:ai.aiot.service.FileHistoryManager.HistoryData.$serializer                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    
{"logs": [{"outputFile": "ai.aiot.app-mergeDebugResources-49:/values-af/values-af.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\095e905be20fb6759056e386840ea417\\transformed\\material3-release\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,290,405,521,621,725,846,987,1115,1257,1342,1441,1531,1627,1742,1863,1967,2095,2220,2352,2518,2643,2765,2888,3017,3108,3207,3323,3449,3549,3659,3762,3899,4039,4145,4243,4320,4414,4508,4593,4681,4786,4867,4950,5049,5147,5242,5340,5428,5531,5631,5734,5850,5931,6031", "endColumns": "116,117,114,115,99,103,120,140,127,141,84,98,89,95,114,120,103,127,124,131,165,124,121,122,128,90,98,115,125,99,109,102,136,139,105,97,76,93,93,84,87,104,80,82,98,97,94,97,87,102,99,102,115,80,99,95", "endOffsets": "167,285,400,516,616,720,841,982,1110,1252,1337,1436,1526,1622,1737,1858,1962,2090,2215,2347,2513,2638,2760,2883,3012,3103,3202,3318,3444,3544,3654,3757,3894,4034,4140,4238,4315,4409,4503,4588,4676,4781,4862,4945,5044,5142,5237,5335,5423,5526,5626,5729,5845,5926,6026,6122"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1471,1588,1706,1821,1937,2037,2141,2262,2403,2531,2673,2758,2857,2947,3043,3158,3279,3383,3511,3636,3768,3934,4059,4181,4304,4433,4524,4623,4739,4865,4965,5075,5178,5315,5455,5561,5659,5736,5830,5924,6009,6097,6202,6283,6366,6465,6563,6658,6756,6844,6947,7047,7150,7266,7347,7447", "endColumns": "116,117,114,115,99,103,120,140,127,141,84,98,89,95,114,120,103,127,124,131,165,124,121,122,128,90,98,115,125,99,109,102,136,139,105,97,76,93,93,84,87,104,80,82,98,97,94,97,87,102,99,102,115,80,99,95", "endOffsets": "1583,1701,1816,1932,2032,2136,2257,2398,2526,2668,2753,2852,2942,3038,3153,3274,3378,3506,3631,3763,3929,4054,4176,4299,4428,4519,4618,4734,4860,4960,5070,5173,5310,5450,5556,5654,5731,5825,5919,6004,6092,6197,6278,6361,6460,6558,6653,6751,6839,6942,7042,7145,7261,7342,7442,7538"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9255637b8829de4ab4fc2e4c44d434fa\\transformed\\core-1.12.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,451,558,667,787", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "148,250,348,446,553,662,782,883"}, "to": {"startLines": "2,3,4,5,6,7,8,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,403,501,608,717,8078", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "198,300,398,496,603,712,832,8174"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7a9eb4a1e8c3752182f0d47366147a11\\transformed\\foundation-release\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,84", "endOffsets": "137,222"}, "to": {"startLines": "83,84", "startColumns": "4,4", "startOffsets": "8450,8537", "endColumns": "86,84", "endOffsets": "8532,8617"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\61d702f9086eebe3bb4fe8c633b5114e\\transformed\\ui-release\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,288,385,486,572,648,739,829,915,979,1044,1122,1203,1274,1355,1425", "endColumns": "95,86,96,100,85,75,90,89,85,63,64,77,80,70,80,69,119", "endOffsets": "196,283,380,481,567,643,734,824,910,974,1039,1117,1198,1269,1350,1420,1540"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "837,933,1020,1117,1218,1304,1380,7543,7633,7719,7783,7848,7926,8007,8179,8260,8330", "endColumns": "95,86,96,100,85,75,90,89,85,63,64,77,80,70,80,69,119", "endOffsets": "928,1015,1112,1213,1299,1375,1466,7628,7714,7778,7843,7921,8002,8073,8255,8325,8445"}}]}]}
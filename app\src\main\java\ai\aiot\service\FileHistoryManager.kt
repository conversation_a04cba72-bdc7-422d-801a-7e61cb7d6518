package ai.aiot.service

import android.content.Context
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import java.io.File

/**
 * 文件历史记录管理器
 */
class FileHistoryManager(private val context: Context) {
    
    private val json = Json {
        ignoreUnknownKeys = true
        encodeDefaults = true
        prettyPrint = true
    }
    
    private val historyFile = File(context.filesDir, "file_history.json")
    
    /**
     * 文件历史记录项
     */
    @Serializable
    data class FileHistoryItem(
        val path: String,
        val name: String,
        val lastOpenTime: Long,
        val openCount: Int = 1,
        val projectPath: String? = null
    )
    
    /**
     * 文件夹展开状态记录
     */
    @Serializable
    data class FolderState(
        val path: String,
        val isExpanded: Boolean,
        val lastAccessTime: Long
    )
    
    /**
     * 历史记录数据
     */
    @Serializable
    data class HistoryData(
        val recentFiles: List<FileHistoryItem> = emptyList(),
        val folderStates: List<FolderState> = emptyList(),
        val lastProjectPath: String? = null
    )
    
    /**
     * 加载历史记录
     */
    suspend fun loadHistory(): HistoryData = withContext(Dispatchers.IO) {
        try {
            if (!historyFile.exists()) {
                return@withContext HistoryData()
            }
            
            val jsonString = historyFile.readText()
            json.decodeFromString(HistoryData.serializer(), jsonString)
        } catch (e: Exception) {
            HistoryData()
        }
    }
    
    /**
     * 保存历史记录
     */
    suspend fun saveHistory(history: HistoryData) = withContext(Dispatchers.IO) {
        try {
            val jsonString = json.encodeToString(HistoryData.serializer(), history)
            historyFile.writeText(jsonString)
        } catch (e: Exception) {
            // 忽略保存错误
        }
    }
    
    /**
     * 添加文件到历史记录
     */
    suspend fun addFileToHistory(filePath: String, projectPath: String? = null) {
        val history = loadHistory()
        val file = File(filePath)
        
        if (!file.exists()) return
        
        val existingIndex = history.recentFiles.indexOfFirst { it.path == filePath }
        val newItem = if (existingIndex >= 0) {
            // 更新现有记录
            val existing = history.recentFiles[existingIndex]
            existing.copy(
                lastOpenTime = System.currentTimeMillis(),
                openCount = existing.openCount + 1,
                projectPath = projectPath ?: existing.projectPath
            )
        } else {
            // 创建新记录
            FileHistoryItem(
                path = filePath,
                name = file.name,
                lastOpenTime = System.currentTimeMillis(),
                openCount = 1,
                projectPath = projectPath
            )
        }
        
        val updatedFiles = if (existingIndex >= 0) {
            history.recentFiles.toMutableList().apply {
                removeAt(existingIndex)
                add(0, newItem)
            }
        } else {
            listOf(newItem) + history.recentFiles
        }.take(20) // 保留最近20个文件
        
        val updatedHistory = history.copy(recentFiles = updatedFiles)
        saveHistory(updatedHistory)
    }
    
    /**
     * 获取最近打开的文件
     */
    suspend fun getRecentFiles(limit: Int = 10): List<FileHistoryItem> {
        val history = loadHistory()
        return history.recentFiles
            .filter { File(it.path).exists() } // 过滤不存在的文件
            .take(limit)
    }
    
    /**
     * 保存文件夹展开状态
     */
    suspend fun saveFolderState(folderPath: String, isExpanded: Boolean) {
        val history = loadHistory()
        val existingIndex = history.folderStates.indexOfFirst { it.path == folderPath }
        
        val newState = FolderState(
            path = folderPath,
            isExpanded = isExpanded,
            lastAccessTime = System.currentTimeMillis()
        )
        
        val updatedStates = if (existingIndex >= 0) {
            history.folderStates.toMutableList().apply {
                set(existingIndex, newState)
            }
        } else {
            history.folderStates + newState
        }.take(100) // 保留最近100个文件夹状态
        
        val updatedHistory = history.copy(folderStates = updatedStates)
        saveHistory(updatedHistory)
    }
    
    /**
     * 获取文件夹展开状态
     */
    suspend fun getFolderState(folderPath: String): Boolean? {
        val history = loadHistory()
        return history.folderStates.find { it.path == folderPath }?.isExpanded
    }
    
    /**
     * 保存最后打开的项目路径
     */
    suspend fun saveLastProjectPath(projectPath: String) {
        val history = loadHistory()
        val updatedHistory = history.copy(lastProjectPath = projectPath)
        saveHistory(updatedHistory)
    }
    
    /**
     * 获取最后打开的项目路径
     */
    suspend fun getLastProjectPath(): String? {
        val history = loadHistory()
        return history.lastProjectPath
    }
    
    /**
     * 清除历史记录
     */
    suspend fun clearHistory() = withContext(Dispatchers.IO) {
        try {
            if (historyFile.exists()) {
                historyFile.delete()
            }
        } catch (e: Exception) {
            // 忽略删除错误
        }
    }
}

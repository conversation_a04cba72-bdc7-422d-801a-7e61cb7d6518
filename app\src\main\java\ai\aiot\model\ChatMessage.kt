package ai.aiot.model

import kotlinx.serialization.Serializable

/**
 * 聊天消息数据模型
 */
@Serializable
data class ChatMessage(
    val id: String,
    val content: String,
    val isFromUser: Boolean,
    val timestamp: Long = System.currentTimeMillis(),
    val messageType: MessageType = MessageType.TEXT,
    val codeSnippet: CodeSnippet? = null,
    val operation: AIOperationRequest? = null // 关联的AI操作
) {
    /**
     * 获取消息类型（用于AI API）
     */
    val type: MessageType
        get() = when {
            isFromUser -> MessageType.USER
            messageType == MessageType.SYSTEM -> MessageType.SYSTEM
            else -> MessageType.AI
        }
}

/**
 * 消息类型
 */
@Serializable
enum class MessageType {
    TEXT,
    CODE,
    ERROR,
    SYSTEM,
    USER,
    AI
}

/**
 * 代码片段
 */
@Serializable
data class CodeSnippet(
    val code: String,
    val language: String,
    val fileName: String? = null
)



package ai.aiot.model

import kotlinx.serialization.Serializable

/**
 * AI配置数据模型
 */
@Serializable
data class AIConfig(
    val provider: AIProvider = AIProvider.OPENAI,
    val apiKey: String = "",
    val apiUrl: String = "",
    val model: String = "",
    val maxTokens: Int = 4000,
    val temperature: Float = 0.7f,
    val timeout: Long = 30000L, // 30秒
    val isEnabled: Boolean = false,
    val customHeaders: Map<String, String> = emptyMap()
) {
    /**
     * 检查配置是否有效
     */
    fun isValid(): Boolean {
        return apiKey.isNotBlank() && 
               apiUrl.isNotBlank() && 
               model.isNotBlank()
    }
    
    /**
     * 获取默认配置
     */
    fun getDefaultConfig(): AIConfig {
        return when (provider) {
            AIProvider.OPENAI -> copy(
                apiUrl = "https://api.openai.com/v1/chat/completions",
                model = "gpt-3.5-turbo"
            )
            AIProvider.CLAUDE -> copy(
                apiUrl = "https://api.anthropic.com/v1/messages",
                model = "claude-3-sonnet-20240229"
            )
            AIProvider.GEMINI -> copy(
                apiUrl = "https://generativelanguage.googleapis.com/v1beta/models",
                model = "gemini-pro"
            )
            AIProvider.DEEPSEEK -> copy(
                apiUrl = "https://api.deepseek.com/v1/chat/completions",
                model = "deepseek-chat"
            )
            AIProvider.CUSTOM -> this
        }
    }
}

/**
 * AI服务提供商
 */
@Serializable
enum class AIProvider(
    val displayName: String,
    val description: String,
    val defaultModel: String,
    val supportedModels: List<String>
) {
    OPENAI(
        displayName = "OpenAI",
        description = "OpenAI GPT系列模型",
        defaultModel = "gpt-3.5-turbo",
        supportedModels = listOf(
            "gpt-3.5-turbo",
            "gpt-3.5-turbo-16k",
            "gpt-4",
            "gpt-4-turbo-preview",
            "gpt-4-vision-preview"
        )
    ),
    
    CLAUDE(
        displayName = "Claude",
        description = "Anthropic Claude系列模型",
        defaultModel = "claude-3-sonnet-20240229",
        supportedModels = listOf(
            "claude-3-haiku-20240307",
            "claude-3-sonnet-20240229",
            "claude-3-opus-20240229"
        )
    ),
    
    GEMINI(
        displayName = "Gemini",
        description = "Google Gemini系列模型",
        defaultModel = "gemini-pro",
        supportedModels = listOf(
            "gemini-pro",
            "gemini-pro-vision"
        )
    ),

    DEEPSEEK(
        displayName = "DeepSeek",
        description = "DeepSeek系列模型",
        defaultModel = "deepseek-chat",
        supportedModels = listOf(
            "deepseek-chat",
            "deepseek-coder"
        )
    ),

    CUSTOM(
        displayName = "自定义",
        description = "自定义AI服务",
        defaultModel = "",
        supportedModels = emptyList()
    )
}

/**
 * AI请求数据模型
 */
@Serializable
data class AIRequest(
    val messages: List<AIMessage>,
    val model: String,
    val maxTokens: Int = 4000,
    val temperature: Float = 0.7f,
    val stream: Boolean = false
)

/**
 * AI消息数据模型
 */
@Serializable
data class AIMessage(
    val role: String, // "system", "user", "assistant"
    val content: String
)

/**
 * AI响应数据模型
 */
@Serializable
data class AIResponse(
    val id: String = "",
    val choices: List<AIChoice> = emptyList(),
    val usage: AIUsage? = null,
    val error: AIError? = null
) {
    fun getContent(): String {
        return choices.firstOrNull()?.message?.content ?: ""
    }
    
    fun isSuccess(): Boolean {
        return error == null && choices.isNotEmpty()
    }
}

/**
 * AI选择数据模型
 */
@Serializable
data class AIChoice(
    val index: Int = 0,
    val message: AIMessage,
    val finishReason: String? = null
)

/**
 * AI使用统计
 */
@Serializable
data class AIUsage(
    val promptTokens: Int = 0,
    val completionTokens: Int = 0,
    val totalTokens: Int = 0
)

/**
 * AI错误信息
 */
@Serializable
data class AIError(
    val message: String,
    val type: String? = null,
    val code: String? = null
)

/**
 * 代码编辑请求
 */
@Serializable
data class CodeEditRequest(
    val filePath: String,
    val fileName: String,
    val language: String,
    val content: String,
    val lineRange: String, // 16进制行号范围，如 [0x64-0xC8]
    val instruction: String,
    val context: String = "" // 额外上下文信息
) {
    /**
     * 生成AI提示词
     */
    fun generatePrompt(): String {
        return buildString {
            appendLine("你是一个专业的代码编辑助手。请根据以下信息修改代码：")
            appendLine()
            appendLine("文件信息：")
            appendLine("- 文件名：$fileName")
            appendLine("- 语言：$language")
            appendLine("- 行号范围：$lineRange")
            appendLine()
            appendLine("修改指令：")
            appendLine(instruction)
            appendLine()
            if (context.isNotBlank()) {
                appendLine("上下文信息：")
                appendLine(context)
                appendLine()
            }
            appendLine("原始代码：")
            appendLine("```$language")
            appendLine(content)
            appendLine("```")
            appendLine()
            appendLine("请提供修改后的代码，并说明修改原因。格式如下：")
            appendLine("```$language")
            appendLine("// 修改后的代码")
            appendLine("```")
            appendLine()
            appendLine("修改说明：")
            appendLine("// 说明修改的原因和改进点")
        }
    }
}

/**
 * 代码编辑响应
 */
@Serializable
data class CodeEditResponse(
    val originalCode: String,
    val modifiedCode: String,
    val explanation: String,
    val lineRange: String,
    val isApproved: Boolean = false,
    val timestamp: Long = System.currentTimeMillis()
) {
    /**
     * 提取代码块
     */
    fun extractCodeBlock(response: String, language: String): String {
        val codeBlockRegex = "```$language\\s*\\n([\\s\\S]*?)\\n```".toRegex()
        val match = codeBlockRegex.find(response)
        return match?.groupValues?.get(1)?.trim() ?: response
    }
    
    /**
     * 提取说明文本
     */
    fun extractExplanation(response: String): String {
        val explanationRegex = "修改说明：\\s*\\n([\\s\\S]*)".toRegex()
        val match = explanationRegex.find(response)
        return match?.groupValues?.get(1)?.trim() ?: ""
    }
}

/**
 * AI操作类型
 */
@Serializable
enum class AIOperationType(
    val displayName: String,
    val description: String,
    val requiresConfirmation: Boolean
) {
    CREATE_FILE(
        displayName = "创建文件",
        description = "创建新的文件",
        requiresConfirmation = true
    ),
    CREATE_FOLDER(
        displayName = "创建文件夹",
        description = "创建新的文件夹",
        requiresConfirmation = true
    ),
    EDIT_FILE(
        displayName = "编辑文件",
        description = "修改现有文件内容",
        requiresConfirmation = true
    ),
    DELETE_FILE(
        displayName = "删除文件",
        description = "删除指定文件",
        requiresConfirmation = true
    ),
    DELETE_FOLDER(
        displayName = "删除文件夹",
        description = "删除指定文件夹",
        requiresConfirmation = true
    ),
    WEB_SEARCH(
        displayName = "联网搜索",
        description = "搜索网络信息",
        requiresConfirmation = false
    ),
    READ_FILE(
        displayName = "读取文件",
        description = "读取文件内容",
        requiresConfirmation = false
    ),
    LIST_FILES(
        displayName = "列出文件",
        description = "列出目录中的文件",
        requiresConfirmation = false
    )
}

/**
 * AI操作请求
 */
@Serializable
data class AIOperationRequest(
    val type: AIOperationType,
    val path: String,
    val content: String = "",
    val parameters: Map<String, String> = emptyMap(),
    val timestamp: Long = System.currentTimeMillis()
) {
    fun generatePrompt(): String {
        return when (type) {
            AIOperationType.CREATE_FILE -> {
                "创建文件：$path\n内容：\n$content"
            }
            AIOperationType.CREATE_FOLDER -> {
                "创建文件夹：$path"
            }
            AIOperationType.EDIT_FILE -> {
                "编辑文件：$path\n修改内容：\n$content"
            }
            AIOperationType.DELETE_FILE -> {
                "删除文件：$path"
            }
            AIOperationType.DELETE_FOLDER -> {
                "删除文件夹：$path"
            }
            AIOperationType.WEB_SEARCH -> {
                "搜索：$content"
            }
            AIOperationType.READ_FILE -> {
                "读取文件：$path"
            }
            AIOperationType.LIST_FILES -> {
                "列出文件：$path"
            }
        }
    }
}

/**
 * AI操作响应
 */
@Serializable
data class AIOperationResponse(
    val request: AIOperationRequest,
    val success: Boolean,
    val result: String,
    val error: String? = null,
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * 对话历史记录
 */
@Serializable
data class ConversationHistory(
    val id: String,
    val messages: MutableList<ChatMessage> = mutableListOf(),
    val operations: MutableList<AIOperationResponse> = mutableListOf(),
    val maxMessages: Int = 50, // 最大消息数量
    val maxTokens: Int = 8000, // 最大token数量
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
) {
    /**
     * 添加消息
     */
    fun addMessage(message: ChatMessage) {
        messages.add(message)
        trimHistory()
    }

    /**
     * 添加操作记录
     */
    fun addOperation(operation: AIOperationResponse) {
        operations.add(operation)
        trimOperations()
    }

    /**
     * 修剪历史记录以保持在限制范围内
     */
    private fun trimHistory() {
        while (messages.size > maxMessages) {
            messages.removeAt(0)
        }

        // 估算token数量并修剪
        var estimatedTokens = 0
        val iterator = messages.listIterator(messages.size)
        val messagesToKeep = mutableListOf<ChatMessage>()

        while (iterator.hasPrevious() && estimatedTokens < maxTokens) {
            val message = iterator.previous()
            val messageTokens = estimateTokens(message.content)
            if (estimatedTokens + messageTokens <= maxTokens) {
                messagesToKeep.add(0, message)
                estimatedTokens += messageTokens
            } else {
                break
            }
        }

        messages.clear()
        messages.addAll(messagesToKeep)
    }

    /**
     * 修剪操作记录
     */
    private fun trimOperations() {
        while (operations.size > 20) { // 保留最近20个操作
            operations.removeAt(0)
        }
    }

    /**
     * 估算token数量（简单估算）
     */
    private fun estimateTokens(text: String): Int {
        return (text.length / 4).coerceAtLeast(1) // 粗略估算：4个字符约等于1个token
    }

    /**
     * 获取上下文消息
     */
    fun getContextMessages(): List<AIMessage> {
        return messages.map { message ->
            AIMessage(
                role = when (message.type) {
                    MessageType.USER -> "user"
                    MessageType.AI -> "assistant"
                    MessageType.SYSTEM -> "system"
                    MessageType.TEXT -> "user"
                    MessageType.CODE -> "user"
                    MessageType.ERROR -> "user"
                },
                content = message.content
            )
        }
    }
}

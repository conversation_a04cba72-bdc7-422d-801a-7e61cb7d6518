1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="ai.aiot"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="29"
9        android:targetSdkVersion="35" />
10
11    <!-- 文件访问权限 -->
12    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
12-->C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:6:5-80
12-->C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:6:22-77
13    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
13-->C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:7:5-81
13-->C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:7:22-78
14    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
14-->C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:8:5-9:40
14-->C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:8:22-79
15
16    <!-- 网络权限（用于AI API调用） -->
17    <uses-permission android:name="android.permission.INTERNET" />
17-->C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:12:5-67
17-->C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:12:22-64
18    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
18-->C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:13:5-79
18-->C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:13:22-76
19
20    <permission
20-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9255637b8829de4ab4fc2e4c44d434fa\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
21        android:name="ai.aiot.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
21-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9255637b8829de4ab4fc2e4c44d434fa\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
22        android:protectionLevel="signature" />
22-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9255637b8829de4ab4fc2e4c44d434fa\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
23
24    <uses-permission android:name="ai.aiot.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
24-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9255637b8829de4ab4fc2e4c44d434fa\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
24-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9255637b8829de4ab4fc2e4c44d434fa\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
25
26    <application
26-->C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:15:5-36:19
27        android:allowBackup="true"
27-->C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:16:9-35
28        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
28-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9255637b8829de4ab4fc2e4c44d434fa\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
29        android:dataExtractionRules="@xml/data_extraction_rules"
29-->C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:17:9-65
30        android:debuggable="true"
31        android:extractNativeLibs="false"
32        android:fullBackupContent="@xml/backup_rules"
32-->C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:18:9-54
33        android:icon="@mipmap/ic_launcher"
33-->C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:19:9-43
34        android:label="@string/app_name"
34-->C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:20:9-41
35        android:roundIcon="@mipmap/ic_launcher_round"
35-->C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:21:9-54
36        android:supportsRtl="true"
36-->C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:22:9-35
37        android:theme="@style/Theme.AIot" >
37-->C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:23:9-42
38        <activity
38-->C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:25:9-35:20
39            android:name="ai.aiot.MainActivity"
39-->C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:26:13-41
40            android:exported="true"
40-->C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:27:13-36
41            android:label="@string/app_name"
41-->C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:28:13-45
42            android:theme="@style/Theme.AIot" >
42-->C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:29:13-46
43            <intent-filter>
43-->C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:30:13-34:29
44                <action android:name="android.intent.action.MAIN" />
44-->C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:31:17-69
44-->C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:31:25-66
45
46                <category android:name="android.intent.category.LAUNCHER" />
46-->C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:33:17-77
46-->C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:33:27-74
47            </intent-filter>
48        </activity>
49        <activity
49-->[androidx.compose.ui:ui-tooling-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70645bf502393932454a9dd718537c81\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
50            android:name="androidx.compose.ui.tooling.PreviewActivity"
50-->[androidx.compose.ui:ui-tooling-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70645bf502393932454a9dd718537c81\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
51            android:exported="true" />
51-->[androidx.compose.ui:ui-tooling-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70645bf502393932454a9dd718537c81\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
52
53        <provider
53-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f451975a29f2bcf44cf51ad5e24c36d\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
54            android:name="androidx.startup.InitializationProvider"
54-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f451975a29f2bcf44cf51ad5e24c36d\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
55            android:authorities="ai.aiot.androidx-startup"
55-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f451975a29f2bcf44cf51ad5e24c36d\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
56            android:exported="false" >
56-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f451975a29f2bcf44cf51ad5e24c36d\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
57            <meta-data
57-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f451975a29f2bcf44cf51ad5e24c36d\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
58                android:name="androidx.emoji2.text.EmojiCompatInitializer"
58-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f451975a29f2bcf44cf51ad5e24c36d\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
59                android:value="androidx.startup" />
59-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f451975a29f2bcf44cf51ad5e24c36d\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
60            <meta-data
60-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61bba18f58263f18fdd415251a46f035\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
61                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
61-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61bba18f58263f18fdd415251a46f035\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
62                android:value="androidx.startup" />
62-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61bba18f58263f18fdd415251a46f035\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
63            <meta-data
63-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
64                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
64-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
65                android:value="androidx.startup" />
65-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
66        </provider>
67
68        <activity
68-->[androidx.compose.ui:ui-test-manifest:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa51a1ddc9f74fbf4af54e56227f4d7a\transformed\ui-test-manifest-1.6.7\AndroidManifest.xml:23:9-25:39
69            android:name="androidx.activity.ComponentActivity"
69-->[androidx.compose.ui:ui-test-manifest:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa51a1ddc9f74fbf4af54e56227f4d7a\transformed\ui-test-manifest-1.6.7\AndroidManifest.xml:24:13-63
70            android:exported="true" />
70-->[androidx.compose.ui:ui-test-manifest:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa51a1ddc9f74fbf4af54e56227f4d7a\transformed\ui-test-manifest-1.6.7\AndroidManifest.xml:25:13-36
71
72        <receiver
72-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
73            android:name="androidx.profileinstaller.ProfileInstallReceiver"
73-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
74            android:directBootAware="false"
74-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
75            android:enabled="true"
75-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
76            android:exported="true"
76-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
77            android:permission="android.permission.DUMP" >
77-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
78            <intent-filter>
78-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
79                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
79-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
79-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
80            </intent-filter>
81            <intent-filter>
81-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
82                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
82-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
82-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
83            </intent-filter>
84            <intent-filter>
84-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
85                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
85-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
85-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
86            </intent-filter>
87            <intent-filter>
87-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
88                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
88-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
88-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
89            </intent-filter>
90        </receiver>
91    </application>
92
93</manifest>

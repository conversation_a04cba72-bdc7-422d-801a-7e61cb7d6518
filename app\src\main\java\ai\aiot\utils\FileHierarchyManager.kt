package ai.aiot.utils

import ai.aiot.model.FileHierarchy
import ai.aiot.model.HierarchyNode
import ai.aiot.model.NodeType
import ai.aiot.model.ProjectFolder
import java.io.File

/**
 * 文件层次结构管理器
 * 实现指定格式的文件层次结构存储：<A><2\a><3\a.txt>|<B>
 * 
 * 格式说明：
 * - <A> 表示第一层文件夹A（1省略不写）
 * - <2\a> 表示第二层文件夹a，数字2表示层级
 * - <3?a.txt> 表示第三层文件a.txt，?表示这是文件
 * - \ 表示文件夹，? 表示文件
 * - | 分隔不同的顶级文件夹
 */
object FileHierarchyManager {
    
    /**
     * 生成项目的完整文件层次结构
     */
    fun generateProjectHierarchy(folders: List<ProjectFolder>): FileHierarchy {
        val hierarchyBuilder = StringBuilder()
        var totalFiles = 0
        var totalFolders = 0
        
        folders.forEachIndexed { index, folder ->
            if (index > 0) {
                hierarchyBuilder.append("|")
            }
            
            val folderResult = generateFolderHierarchy(folder, 1)
            hierarchyBuilder.append(folderResult.hierarchy)
            totalFiles += folderResult.fileCount
            totalFolders += folderResult.folderCount
        }
        
        return FileHierarchy(
            projectId = "",
            hierarchyString = hierarchyBuilder.toString(),
            totalFiles = totalFiles,
            totalFolders = totalFolders
        )
    }
    
    /**
     * 生成单个文件夹的层次结构
     */
    private fun generateFolderHierarchy(
        folder: ProjectFolder,
        level: Int
    ): HierarchyResult {
        val folderFile = File(folder.path)
        if (!folderFile.exists() || !folderFile.isDirectory) {
            return HierarchyResult("", 0, 0)
        }
        
        val builder = StringBuilder()
        var fileCount = 0
        var folderCount = 1 // 当前文件夹
        
        // 添加当前文件夹
        val levelPrefix = if (level == 1) "" else level.toString()
        val folderName = folder.name.ifBlank { folderFile.name }
        builder.append("<$levelPrefix\\$folderName>")
        
        try {
            val children = folderFile.listFiles()
                ?.filter { !isHiddenOrSystem(it) }
                ?.sortedWith(compareBy<File> { !it.isDirectory }.thenBy { it.name })
            
            children?.forEach { child ->
                if (child.isDirectory && folder.includeSubfolders) {
                    // 递归处理子文件夹
                    val childFolder = folder.copy(
                        path = child.absolutePath,
                        name = child.name
                    )
                    val childResult = generateFolderHierarchy(childFolder, level + 1)
                    builder.append(childResult.hierarchy)
                    fileCount += childResult.fileCount
                    folderCount += childResult.folderCount
                    
                } else if (child.isFile && !isExcluded(child.name, folder.excludePatterns)) {
                    // 添加文件
                    val childLevelPrefix = if (level + 1 == 1) "" else (level + 1).toString()
                    builder.append("<$childLevelPrefix?${child.name}>")
                    fileCount++
                }
            }
        } catch (e: SecurityException) {
            // 忽略权限错误
        }
        
        return HierarchyResult(builder.toString(), fileCount, folderCount)
    }
    
    /**
     * 解析层次结构字符串为节点列表
     */
    fun parseHierarchy(hierarchyString: String): List<HierarchyNode> {
        val nodes = mutableListOf<HierarchyNode>()
        
        // 按 | 分割顶级文件夹
        val topLevelParts = hierarchyString.split("|")
        
        topLevelParts.forEach { part ->
            if (part.isNotBlank()) {
                nodes.addAll(parseHierarchyPart(part.trim()))
            }
        }
        
        return nodes
    }
    
    /**
     * 解析单个层次结构部分
     */
    private fun parseHierarchyPart(part: String): List<HierarchyNode> {
        val nodes = mutableListOf<HierarchyNode>()
        
        // 正则表达式匹配 <level\name> 或 <level?name> 格式
        val regex = "<(\\d*)(\\\\|\\?)?([^<>]+)>".toRegex()
        val matches = regex.findAll(part)
        
        matches.forEach { match ->
            val levelStr = match.groupValues[1]
            val typeSymbol = match.groupValues[2]
            val name = match.groupValues[3]
            
            // 确定层级（空字符串表示第1层）
            val level = if (levelStr.isEmpty()) 1 else levelStr.toInt()
            
            // 确定类型
            val type = when (typeSymbol) {
                "?" -> NodeType.FILE
                "\\" -> NodeType.FOLDER
                else -> NodeType.FOLDER // 默认为文件夹
            }
            
            nodes.add(HierarchyNode(name, type, level))
        }
        
        return nodes
    }
    
    /**
     * 将节点列表转换为层次结构字符串
     */
    fun nodesToHierarchyString(nodes: List<HierarchyNode>): String {
        val builder = StringBuilder()
        var isFirstTopLevel = true
        
        nodes.forEach { node ->
            // 如果是第一层且不是第一个节点，添加分隔符
            if (node.level == 1 && !isFirstTopLevel) {
                builder.append("|")
            }
            
            // 构建节点字符串
            val levelPrefix = if (node.level == 1) "" else node.level.toString()
            val typeSymbol = when (node.type) {
                NodeType.FILE -> "?"
                NodeType.FOLDER -> "\\"
            }
            
            builder.append("<$levelPrefix$typeSymbol${node.name}>")
            
            if (node.level == 1) {
                isFirstTopLevel = false
            }
        }
        
        return builder.toString()
    }
    
    /**
     * 查找指定路径的文件在层次结构中的位置
     */
    fun findFileInHierarchy(
        hierarchyString: String,
        targetPath: String
    ): HierarchyLocation? {
        val nodes = parseHierarchy(hierarchyString)
        val pathParts = targetPath.split(File.separator).filter { it.isNotEmpty() }
        
        var currentLevel = 1
        var currentIndex = 0
        
        for (part in pathParts) {
            val nodeIndex = nodes.drop(currentIndex).indexOfFirst { node ->
                node.level == currentLevel && node.name == part
            }
            
            if (nodeIndex == -1) return null
            
            currentIndex += nodeIndex
            currentLevel++
        }
        
        return HierarchyLocation(currentIndex, nodes[currentIndex])
    }
    
    /**
     * 获取文件的16进制行号表示
     */
    fun getHexLineNumber(lineNumber: Int): String {
        return "0x${lineNumber.toString(16).uppercase()}"
    }
    
    /**
     * 解析16进制行号
     */
    fun parseHexLineNumber(hexString: String): Int? {
        return try {
            if (hexString.startsWith("0x", ignoreCase = true)) {
                hexString.substring(2).toInt(16)
            } else {
                hexString.toInt(16)
            }
        } catch (e: NumberFormatException) {
            null
        }
    }
    
    /**
     * 生成文件的行号范围字符串（16进制）
     */
    fun generateLineRangeString(startLine: Int, endLine: Int): String {
        return "[${getHexLineNumber(startLine)}-${getHexLineNumber(endLine)}]"
    }
    
    /**
     * 检查文件是否为隐藏或系统文件
     */
    private fun isHiddenOrSystem(file: File): Boolean {
        val name = file.name
        return name.startsWith(".") || 
               name.equals("Thumbs.db", ignoreCase = true) ||
               name.equals("desktop.ini", ignoreCase = true) ||
               file.isHidden
    }
    
    /**
     * 检查文件是否被排除
     */
    private fun isExcluded(fileName: String, excludePatterns: List<String>): Boolean {
        return excludePatterns.any { pattern ->
            when {
                pattern.startsWith("*.") -> {
                    val extension = pattern.substring(2)
                    fileName.endsWith(".$extension", ignoreCase = true)
                }
                pattern.contains("*") -> {
                    val regex = pattern.replace("*", ".*").toRegex(RegexOption.IGNORE_CASE)
                    regex.matches(fileName)
                }
                else -> fileName.equals(pattern, ignoreCase = true) || 
                        fileName.contains(pattern, ignoreCase = true)
            }
        }
    }
    
    /**
     * 层次结构生成结果
     */
    private data class HierarchyResult(
        val hierarchy: String,
        val fileCount: Int,
        val folderCount: Int
    )
    
    /**
     * 层次结构位置
     */
    data class HierarchyLocation(
        val index: Int,
        val node: HierarchyNode
    )
    
    /**
     * 验证层次结构字符串格式
     */
    fun validateHierarchyString(hierarchyString: String): ValidationResult {
        return try {
            val nodes = parseHierarchy(hierarchyString)
            if (nodes.isEmpty()) {
                ValidationResult(false, "层次结构为空")
            } else {
                ValidationResult(true, "格式正确")
            }
        } catch (e: Exception) {
            ValidationResult(false, "格式错误: ${e.message}")
        }
    }
    
    /**
     * 验证结果
     */
    data class ValidationResult(
        val isValid: Boolean,
        val message: String
    )
}

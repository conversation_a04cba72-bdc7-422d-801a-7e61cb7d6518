package ai.aiot.utils

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.Settings
import androidx.activity.ComponentActivity
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat

/**
 * 权限管理工具类
 */
class PermissionUtils(private val activity: ComponentActivity) {
    
    private var onPermissionGranted: (() -> Unit)? = null
    private var onPermissionDenied: (() -> Unit)? = null
    
    // 存储权限请求启动器
    private val storagePermissionLauncher = activity.registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.values.all { it }
        if (allGranted) {
            onPermissionGranted?.invoke()
        } else {
            onPermissionDenied?.invoke()
        }
    }
    
    // 管理外部存储权限请求启动器（Android 11+）
    private val manageStorageLauncher = activity.registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            if (Environment.isExternalStorageManager()) {
                onPermissionGranted?.invoke()
            } else {
                onPermissionDenied?.invoke()
            }
        }
    }
    
    /**
     * 检查是否有存储权限
     */
    fun hasStoragePermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            Environment.isExternalStorageManager()
        } else {
            ContextCompat.checkSelfPermission(
                activity,
                Manifest.permission.READ_EXTERNAL_STORAGE
            ) == PackageManager.PERMISSION_GRANTED &&
            ContextCompat.checkSelfPermission(
                activity,
                Manifest.permission.WRITE_EXTERNAL_STORAGE
            ) == PackageManager.PERMISSION_GRANTED
        }
    }
    
    /**
     * 请求存储权限
     */
    fun requestStoragePermission(
        onGranted: () -> Unit,
        onDenied: () -> Unit
    ) {
        onPermissionGranted = onGranted
        onPermissionDenied = onDenied
        
        if (hasStoragePermission()) {
            onGranted()
            return
        }
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11+ 需要请求管理外部存储权限
            try {
                val intent = Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION).apply {
                    data = Uri.parse("package:${activity.packageName}")
                }
                manageStorageLauncher.launch(intent)
            } catch (e: Exception) {
                // 如果无法打开设置页面，回退到普通权限请求
                requestLegacyStoragePermission()
            }
        } else {
            requestLegacyStoragePermission()
        }
    }
    
    /**
     * 请求传统存储权限（Android 10及以下）
     */
    private fun requestLegacyStoragePermission() {
        val permissions = arrayOf(
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
        )
        storagePermissionLauncher.launch(permissions)
    }
    
    /**
     * 检查网络权限
     */
    fun hasNetworkPermission(): Boolean {
        return ContextCompat.checkSelfPermission(
            activity,
            Manifest.permission.INTERNET
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * 获取推荐的文档目录路径
     */
    fun getRecommendedDocumentPath(): String {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11+ 推荐使用应用专用目录
            activity.getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS)?.absolutePath
                ?: activity.filesDir.absolutePath
        } else {
            // Android 10及以下可以使用外部存储
            Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS).absolutePath
        }
    }
    
    /**
     * 获取应用专用目录
     */
    fun getAppSpecificDirectory(): String {
        return activity.getExternalFilesDir(null)?.absolutePath
            ?: activity.filesDir.absolutePath
    }
    
    /**
     * 检查是否应该显示权限说明
     */
    fun shouldShowStoragePermissionRationale(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            false // Android 11+ 不需要显示说明
        } else {
            activity.shouldShowRequestPermissionRationale(Manifest.permission.READ_EXTERNAL_STORAGE) ||
            activity.shouldShowRequestPermissionRationale(Manifest.permission.WRITE_EXTERNAL_STORAGE)
        }
    }
}

package ai.aiot.model

import kotlinx.serialization.Serializable

/**
 * 聊天消息数据模型
 */
@Serializable
data class ChatMessage(
    val id: String,
    val content: String,
    val isFromUser: Boolean,
    val timestamp: Long = System.currentTimeMillis(),
    val messageType: MessageType = MessageType.TEXT,
    val codeSnippet: CodeSnippet? = null
)

/**
 * 消息类型
 */
@Serializable
enum class MessageType {
    TEXT,
    CODE,
    ERROR,
    SYSTEM
}

/**
 * 代码片段
 */
@Serializable
data class CodeSnippet(
    val code: String,
    val language: String,
    val fileName: String? = null
)

/**
 * AI响应数据模型
 */
@Serializable
data class AIResponse(
    val message: String,
    val suggestions: List<String> = emptyList(),
    val codeGenerated: CodeSnippet? = null
)

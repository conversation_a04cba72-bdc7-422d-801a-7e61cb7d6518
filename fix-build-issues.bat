@echo off
echo ========================================
echo AI编程IDE - 构建问题修复脚本
echo ========================================
echo.

echo 1. 清理Gradle缓存...
if exist .gradle (
    rmdir /s /q .gradle
    echo    ✓ 已删除 .gradle 目录
) else (
    echo    ✓ .gradle 目录不存在
)

echo.
echo 2. 清理构建目录...
if exist build (
    rmdir /s /q build
    echo    ✓ 已删除 build 目录
) else (
    echo    ✓ build 目录不存在
)

if exist app\build (
    rmdir /s /q app\build
    echo    ✓ 已删除 app\build 目录
) else (
    echo    ✓ app\build 目录不存在
)

echo.
echo 3. 清理本地属性...
if exist local.properties (
    del local.properties
    echo    ✓ 已删除 local.properties
) else (
    echo    ✓ local.properties 不存在
)

echo.
echo 4. 重新生成Gradle Wrapper...
call gradlew wrapper --gradle-version=8.11.1
if %errorlevel% equ 0 (
    echo    ✓ Gradle Wrapper 更新成功
) else (
    echo    ✗ Gradle Wrapper 更新失败
)

echo.
echo ========================================
echo 修复完成！
echo ========================================
echo.
echo 接下来请：
echo 1. 在Android Studio中打开项目
echo 2. 等待Gradle同步完成
echo 3. 如果仍有问题，请运行：
echo    - Build ^> Clean Project
echo    - Build ^> Rebuild Project
echo    - File ^> Invalidate Caches and Restart
echo.
pause

package ai.aiot.viewmodel

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import ai.aiot.model.FileItem
import ai.aiot.service.FileHistoryManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.io.File

/**
 * 文件管理器ViewModel
 */
class FileManagerViewModel(private val context: Context) : ViewModel() {

    private val historyManager = FileHistoryManager(context)
    
    private val _fileTree = MutableStateFlow<List<FileItem>>(emptyList())
    val fileTree: StateFlow<List<FileItem>> = _fileTree.asStateFlow()
    
    private val _currentPath = MutableStateFlow("")
    val currentPath: StateFlow<String> = _currentPath.asStateFlow()
    
    private val _selectedFile = MutableStateFlow<FileItem?>(null)
    val selectedFile: StateFlow<FileItem?> = _selectedFile.asStateFlow()
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    private val _recentFiles = MutableStateFlow<List<FileHistoryManager.FileHistoryItem>>(emptyList())
    val recentFiles: StateFlow<List<FileHistoryManager.FileHistoryItem>> = _recentFiles.asStateFlow()
    
    /**
     * 加载指定路径的文件
     */
    fun loadFiles(path: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _errorMessage.value = null
                
                val directory = File(path)
                if (!directory.exists() || !directory.isDirectory) {
                    _errorMessage.value = "目录不存在或不是有效目录"
                    return@launch
                }
                
                val files = directory.listFiles()?.map { file ->
                    FileItem.fromFile(file).apply {
                        if (isDirectory) {
                            // 恢复文件夹展开状态
                            viewModelScope.launch {
                                val savedState = historyManager.getFolderState(<EMAIL>)
                                if (savedState == true) {
                                    <EMAIL> = true
                                    loadChildren(this@apply)
                                }
                            }
                        }
                    }
                }?.sortedWith(compareBy<FileItem> { !it.isDirectory }.thenBy { it.name }) ?: emptyList()
                
                _fileTree.value = files
                _currentPath.value = path
                
            } catch (e: Exception) {
                _errorMessage.value = "加载文件失败: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 展开或折叠文件夹
     */
    fun toggleFolder(fileItem: FileItem) {
        if (!fileItem.isDirectory) return
        
        viewModelScope.launch {
            try {
                fileItem.isExpanded = !fileItem.isExpanded

                // 保存文件夹展开状态
                historyManager.saveFolderState(fileItem.path, fileItem.isExpanded)

                if (fileItem.isExpanded && fileItem.children.isEmpty()) {
                    loadChildren(fileItem)
                }

                // 更新UI
                _fileTree.value = _fileTree.value.toList()
                
            } catch (e: Exception) {
                _errorMessage.value = "展开文件夹失败: ${e.message}"
            }
        }
    }
    
    /**
     * 选择文件
     */
    fun selectFile(fileItem: FileItem) {
        _selectedFile.value = fileItem

        // 如果是文件（非目录），添加到历史记录
        if (!fileItem.isDirectory) {
            viewModelScope.launch {
                historyManager.addFileToHistory(fileItem.path, _currentPath.value)
                loadRecentFiles()
            }
        }
    }

    /**
     * 加载最近文件
     */
    fun loadRecentFiles() {
        viewModelScope.launch {
            val recent = historyManager.getRecentFiles()
            _recentFiles.value = recent
        }
    }
    
    /**
     * 创建新文件
     */
    fun createFile(parentPath: String, fileName: String): Boolean {
        return try {
            println("FileManagerViewModel.createFile: parentPath=$parentPath, fileName=$fileName")

            // 清理文件名，移除非法字符
            val cleanFileName = fileName.trim().replace(Regex("[<>:\"/\\\\|?*]"), "_")
            if (cleanFileName.isEmpty()) {
                _errorMessage.value = "文件名不能为空"
                return false
            }

            println("清理后的文件名: $cleanFileName")

            val parentDir = File(parentPath)
            println("父目录: ${parentDir.absolutePath}, 存在: ${parentDir.exists()}, 是目录: ${parentDir.isDirectory}")
            if (!parentDir.exists()) {
                // 尝试创建父目录
                val created = parentDir.mkdirs()
                if (!created) {
                    _errorMessage.value = "无法创建父目录: $parentPath"
                    return false
                }
            }

            if (!parentDir.isDirectory) {
                _errorMessage.value = "路径不是目录: $parentPath"
                return false
            }

            val newFile = File(parentDir, cleanFileName)

            // 检查文件是否已存在
            if (newFile.exists()) {
                _errorMessage.value = "文件已存在: $cleanFileName"
                return false
            }

            // 创建文件
            val created = newFile.createNewFile()
            if (created) {
                // 刷新当前目录显示
                loadFiles(_currentPath.value)
                _errorMessage.value = null
                return true
            } else {
                _errorMessage.value = "创建文件失败: $cleanFileName"
                return false
            }
        } catch (e: SecurityException) {
            _errorMessage.value = "权限不足，无法创建文件: ${e.message}"
            false
        } catch (e: Exception) {
            _errorMessage.value = "创建文件失败: ${e.message}"
            false
        }
    }
    
    /**
     * 创建新文件夹
     */
    fun createFolder(parentPath: String, folderName: String): Boolean {
        return try {
            // 清理文件夹名，移除非法字符
            val cleanFolderName = folderName.trim().replace(Regex("[<>:\"/\\\\|?*]"), "_")
            if (cleanFolderName.isEmpty()) {
                _errorMessage.value = "文件夹名不能为空"
                return false
            }

            val parentDir = File(parentPath)
            if (!parentDir.exists()) {
                // 尝试创建父目录
                val created = parentDir.mkdirs()
                if (!created) {
                    _errorMessage.value = "无法创建父目录: $parentPath"
                    return false
                }
            }

            if (!parentDir.isDirectory) {
                _errorMessage.value = "路径不是目录: $parentPath"
                return false
            }

            val newFolder = File(parentDir, cleanFolderName)

            // 检查文件夹是否已存在
            if (newFolder.exists()) {
                _errorMessage.value = "文件夹已存在: $cleanFolderName"
                return false
            }

            // 创建文件夹
            val created = newFolder.mkdirs()
            if (created) {
                // 刷新当前目录显示
                loadFiles(_currentPath.value)
                _errorMessage.value = null
                return true
            } else {
                _errorMessage.value = "创建文件夹失败: $cleanFolderName"
                return false
            }
        } catch (e: SecurityException) {
            _errorMessage.value = "权限不足，无法创建文件夹: ${e.message}"
            false
        } catch (e: Exception) {
            _errorMessage.value = "创建文件夹失败: ${e.message}"
            false
        }
    }
    
    /**
     * 删除文件或文件夹
     */
    fun deleteFile(fileItem: FileItem): Boolean {
        return try {
            val file = File(fileItem.path)
            val deleted = if (file.isDirectory) {
                file.deleteRecursively()
            } else {
                file.delete()
            }
            
            if (deleted) {
                refreshCurrentDirectory()
                if (_selectedFile.value?.path == fileItem.path) {
                    _selectedFile.value = null
                }
            } else {
                _errorMessage.value = "删除失败"
            }
            
            deleted
        } catch (e: Exception) {
            _errorMessage.value = "删除失败: ${e.message}"
            false
        }
    }
    
    /**
     * 重命名文件或文件夹
     */
    fun renameFile(fileItem: FileItem, newName: String): Boolean {
        return try {
            val oldFile = File(fileItem.path)
            val newFile = File(oldFile.parent, newName)
            
            if (newFile.exists()) {
                _errorMessage.value = "目标名称已存在"
                false
            } else {
                val renamed = oldFile.renameTo(newFile)
                if (renamed) {
                    refreshCurrentDirectory()
                    if (_selectedFile.value?.path == fileItem.path) {
                        _selectedFile.value = _selectedFile.value?.copy(
                            name = newName,
                            path = newFile.absolutePath
                        )
                    }
                } else {
                    _errorMessage.value = "重命名失败"
                }
                renamed
            }
        } catch (e: Exception) {
            _errorMessage.value = "重命名失败: ${e.message}"
            false
        }
    }
    
    /**
     * 加载子文件
     */
    private fun loadChildren(parent: FileItem) {
        try {
            val directory = File(parent.path)
            val children = directory.listFiles()?.map { file ->
                FileItem.fromFile(file)
            }?.sortedWith(compareBy<FileItem> { !it.isDirectory }.thenBy { it.name }) ?: emptyList()
            
            parent.children.clear()
            parent.children.addAll(children)
        } catch (e: Exception) {
            _errorMessage.value = "加载子文件失败: ${e.message}"
        }
    }
    
    /**
     * 刷新当前目录
     */
    private fun refreshCurrentDirectory() {
        val currentPath = _currentPath.value
        if (currentPath.isNotEmpty()) {
            loadFiles(currentPath)
        }
    }
    
    /**
     * 设置错误消息
     */
    fun setError(message: String) {
        _errorMessage.value = message
    }

    /**
     * 清除错误消息
     */
    fun clearError() {
        _errorMessage.value = null
    }
}

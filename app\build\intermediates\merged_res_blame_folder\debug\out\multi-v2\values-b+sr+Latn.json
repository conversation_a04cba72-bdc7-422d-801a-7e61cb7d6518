{"logs": [{"outputFile": "ai.aiot.app-mergeDebugResources-49:/values-b+sr+Latn/values-b+sr+Latn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7a9eb4a1e8c3752182f0d47366147a11\\transformed\\foundation-release\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,91", "endOffsets": "140,232"}, "to": {"startLines": "83,84", "startColumns": "4,4", "startOffsets": "8565,8655", "endColumns": "89,91", "endOffsets": "8650,8742"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9255637b8829de4ab4fc2e4c44d434fa\\transformed\\core-1.12.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "2,3,4,5,6,7,8,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,402,506,610,715,8197", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "198,300,397,501,605,710,826,8293"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\095e905be20fb6759056e386840ea417\\transformed\\material3-release\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,289,417,534,633,727,838,974,1094,1236,1321,1421,1516,1614,1730,1855,1960,2101,2241,2374,2554,2679,2799,2924,3046,3142,3240,3358,3488,3588,3690,3799,3941,4090,4199,4302,4379,4478,4576,4665,4751,4858,4938,5021,5118,5221,5314,5412,5499,5607,5704,5806,5939,6019,6128", "endColumns": "116,116,127,116,98,93,110,135,119,141,84,99,94,97,115,124,104,140,139,132,179,124,119,124,121,95,97,117,129,99,101,108,141,148,108,102,76,98,97,88,85,106,79,82,96,102,92,97,86,107,96,101,132,79,108,98", "endOffsets": "167,284,412,529,628,722,833,969,1089,1231,1316,1416,1511,1609,1725,1850,1955,2096,2236,2369,2549,2674,2794,2919,3041,3137,3235,3353,3483,3583,3685,3794,3936,4085,4194,4297,4374,4473,4571,4660,4746,4853,4933,5016,5113,5216,5309,5407,5494,5602,5699,5801,5934,6014,6123,6222"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1467,1584,1701,1829,1946,2045,2139,2250,2386,2506,2648,2733,2833,2928,3026,3142,3267,3372,3513,3653,3786,3966,4091,4211,4336,4458,4554,4652,4770,4900,5000,5102,5211,5353,5502,5611,5714,5791,5890,5988,6077,6163,6270,6350,6433,6530,6633,6726,6824,6911,7019,7116,7218,7351,7431,7540", "endColumns": "116,116,127,116,98,93,110,135,119,141,84,99,94,97,115,124,104,140,139,132,179,124,119,124,121,95,97,117,129,99,101,108,141,148,108,102,76,98,97,88,85,106,79,82,96,102,92,97,86,107,96,101,132,79,108,98", "endOffsets": "1579,1696,1824,1941,2040,2134,2245,2381,2501,2643,2728,2828,2923,3021,3137,3262,3367,3508,3648,3781,3961,4086,4206,4331,4453,4549,4647,4765,4895,4995,5097,5206,5348,5497,5606,5709,5786,5885,5983,6072,6158,6265,6345,6428,6525,6628,6721,6819,6906,7014,7111,7213,7346,7426,7535,7634"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\61d702f9086eebe3bb4fe8c633b5114e\\transformed\\ui-release\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,289,386,487,573,650,741,833,918,990,1061,1141,1226,1299,1378,1448", "endColumns": "96,86,96,100,85,76,90,91,84,71,70,79,84,72,78,69,117", "endOffsets": "197,284,381,482,568,645,736,828,913,985,1056,1136,1221,1294,1373,1443,1561"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "831,928,1015,1112,1213,1299,1376,7639,7731,7816,7888,7959,8039,8124,8298,8377,8447", "endColumns": "96,86,96,100,85,76,90,91,84,71,70,79,84,72,78,69,117", "endOffsets": "923,1010,1107,1208,1294,1371,1462,7726,7811,7883,7954,8034,8119,8192,8372,8442,8560"}}]}]}
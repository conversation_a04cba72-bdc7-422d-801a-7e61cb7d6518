package ai.aiot.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import ai.aiot.model.FileItem
import ai.aiot.model.FileType
import ai.aiot.viewmodel.FileManagerViewModel
import java.io.File

/**
 * 文件管理面板组件
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FileManagerPanel(
    viewModel: FileManagerViewModel,
    onFileSelected: (FileItem) -> Unit,
    modifier: Modifier = Modifier,
    currentProjectPath: String? = null
) {
    val fileTree by viewModel.fileTree.collectAsState()
    val selectedFile by viewModel.selectedFile.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val errorMessage by viewModel.errorMessage.collectAsState()
    val recentFiles by viewModel.recentFiles.collectAsState()

    // 加载最近文件
    LaunchedEffect(Unit) {
        viewModel.loadRecentFiles()
    }
    
    var showCreateDialog by remember { mutableStateOf(false) }
    var createDialogType by remember { mutableStateOf(CreateType.FILE) }
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.surface)
    ) {
        // 标题栏
        FileManagerHeader(
            onCreateFile = { 
                createDialogType = CreateType.FILE
                showCreateDialog = true 
            },
            onCreateFolder = { 
                createDialogType = CreateType.FOLDER
                showCreateDialog = true 
            },
            onRefresh = { 
                // 这里可以添加刷新逻辑
            }
        )
        
        Divider()

        // 最近文件面板（如果有最近文件）
        if (recentFiles.isNotEmpty()) {
            CompactRecentFilesPanel(
                recentFiles = recentFiles,
                onFileSelected = { filePath ->
                    // 打开文件
                    val fileItem = FileItem(
                        name = File(filePath).name,
                        path = filePath,
                        isDirectory = false,
                        size = File(filePath).length(),
                        lastModified = File(filePath).lastModified()
                    )
                    viewModel.selectFile(fileItem)
                    onFileSelected(fileItem)
                }
            )
            Divider()
        }

        // 文件列表
        Box(modifier = Modifier.fillMaxSize()) {
            when {
                isLoading -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator()
                    }
                }
                
                errorMessage != null -> {
                    ErrorMessage(
                        message = errorMessage!!,
                        onDismiss = { viewModel.clearError() }
                    )
                }
                
                fileTree.isEmpty() -> {
                    EmptyFileList()
                }
                
                else -> {
                    // 创建扁平化的文件列表
                    val flatFileList = remember(fileTree) {
                        buildList {
                            fileTree.forEach { fileItem ->
                                add(fileItem to 0)
                                if (fileItem.isDirectory && fileItem.isExpanded) {
                                    fileItem.children.forEach { childItem ->
                                        add(childItem to 1)
                                    }
                                }
                            }
                        }
                    }

                    LazyColumn(
                        modifier = Modifier.fillMaxSize(),
                        contentPadding = PaddingValues(4.dp),
                        verticalArrangement = Arrangement.spacedBy(2.dp)
                    ) {
                        items(flatFileList) { (fileItem, level) ->
                            FileItemRow(
                                fileItem = fileItem,
                                isSelected = selectedFile?.path == fileItem.path,
                                onItemClick = { item ->
                                    viewModel.selectFile(item)
                                    if (!item.isDirectory) {
                                        onFileSelected(item)
                                    }
                                },
                                onToggleExpand = { item ->
                                    viewModel.toggleFolder(item)
                                },
                                level = level
                            )
                        }
                    }
                }
            }
        }
    }
    
    // 创建文件/文件夹对话框
    if (showCreateDialog) {
        CreateItemDialog(
            type = createDialogType,
            onConfirm = { name ->
                // 使用当前项目路径或选中文件的父目录
                val currentSelectedFile = selectedFile
                val targetPath = when {
                    currentSelectedFile?.isDirectory == true -> currentSelectedFile.path
                    currentSelectedFile != null -> File(currentSelectedFile.path).parent ?: currentProjectPath
                    else -> currentProjectPath
                } ?: run {
                    viewModel.setError("无法确定创建位置，请先选择一个文件夹")
                    showCreateDialog = false
                    return@CreateItemDialog
                }

                when (createDialogType) {
                    CreateType.FILE -> {
                        if (viewModel.createFile(targetPath, name)) {
                            showCreateDialog = false
                        }
                    }
                    CreateType.FOLDER -> {
                        if (viewModel.createFolder(targetPath, name)) {
                            showCreateDialog = false
                        }
                    }
                }
            },
            onDismiss = { showCreateDialog = false }
        )
    }
}

/**
 * 文件管理器标题栏
 */
@Composable
private fun FileManagerHeader(
    onCreateFile: () -> Unit,
    onCreateFolder: () -> Unit,
    onRefresh: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(8.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = "文件管理器",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold
        )
        
        Row {
            IconButton(onClick = onCreateFile) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "新建文件"
                )
            }
            IconButton(onClick = onCreateFolder) {
                Icon(
                    imageVector = Icons.Default.CreateNewFolder,
                    contentDescription = "新建文件夹"
                )
            }
            IconButton(onClick = onRefresh) {
                Icon(
                    imageVector = Icons.Default.Refresh,
                    contentDescription = "刷新"
                )
            }
        }
    }
}

/**
 * 文件项行组件
 */
@Composable
private fun FileItemRow(
    fileItem: FileItem,
    isSelected: Boolean,
    onItemClick: (FileItem) -> Unit,
    onToggleExpand: (FileItem) -> Unit,
    level: Int
) {
    val backgroundColor = if (isSelected) {
        MaterialTheme.colorScheme.primaryContainer
    } else {
        Color.Transparent
    }
    
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(4.dp))
            .background(backgroundColor)
            .clickable { onItemClick(fileItem) }
            .padding(
                start = (level * 16 + 8).dp,
                top = 4.dp,
                end = 8.dp,
                bottom = 4.dp
            ),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 展开/折叠图标
        if (fileItem.isDirectory) {
            IconButton(
                onClick = { onToggleExpand(fileItem) },
                modifier = Modifier.size(24.dp)
            ) {
                Icon(
                    imageVector = if (fileItem.isExpanded) {
                        Icons.Default.ExpandLess
                    } else {
                        Icons.Default.ExpandMore
                    },
                    contentDescription = if (fileItem.isExpanded) "折叠" else "展开",
                    modifier = Modifier.size(16.dp)
                )
            }
        } else {
            Spacer(modifier = Modifier.width(24.dp))
        }
        
        // 文件图标
        Icon(
            imageVector = getFileIcon(fileItem.getFileType()),
            contentDescription = null,
            modifier = Modifier.size(20.dp),
            tint = getFileIconColor(fileItem.getFileType())
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        // 文件名
        Text(
            text = fileItem.name,
            style = MaterialTheme.typography.bodyMedium,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier.weight(1f)
        )
        
        // 文件大小（仅文件显示）
        if (!fileItem.isDirectory && fileItem.size > 0) {
            Text(
                text = formatFileSize(fileItem.size),
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                fontSize = 10.sp
            )
        }
    }
}

/**
 * 空文件列表提示
 */
@Composable
private fun EmptyFileList() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.FolderOpen,
                contentDescription = null,
                modifier = Modifier.size(48.dp),
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "暂无文件",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * 错误消息组件
 */
@Composable
private fun ErrorMessage(
    message: String,
    onDismiss: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.errorContainer
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.Error,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onErrorContainer
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = message,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onErrorContainer,
                modifier = Modifier.weight(1f)
            )
            IconButton(onClick = onDismiss) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = "关闭",
                    tint = MaterialTheme.colorScheme.onErrorContainer
                )
            }
        }
    }
}

/**
 * 获取文件图标
 */
private fun getFileIcon(fileType: FileType): ImageVector {
    return when (fileType) {
        FileType.FOLDER -> Icons.Default.Folder
        FileType.CODE_KOTLIN -> Icons.Default.Code
        FileType.CODE_XML -> Icons.Default.Code
        FileType.CODE_JSON -> Icons.Default.DataObject
        FileType.CODE_GRADLE -> Icons.Default.Build
        FileType.CODE_GENERIC -> Icons.Default.Code
        FileType.FILE_GENERIC -> Icons.Default.InsertDriveFile
    }
}

/**
 * 获取文件图标颜色
 */
@Composable
private fun getFileIconColor(fileType: FileType): Color {
    return when (fileType) {
        FileType.FOLDER -> MaterialTheme.colorScheme.primary
        FileType.CODE_KOTLIN -> Color(0xFF7F52FF)
        FileType.CODE_XML -> Color(0xFFFF6B35)
        FileType.CODE_JSON -> Color(0xFF4CAF50)
        FileType.CODE_GRADLE -> Color(0xFF02303A)
        FileType.CODE_GENERIC -> MaterialTheme.colorScheme.secondary
        FileType.FILE_GENERIC -> MaterialTheme.colorScheme.onSurfaceVariant
    }
}

/**
 * 格式化文件大小
 */
private fun formatFileSize(bytes: Long): String {
    val kb = bytes / 1024.0
    val mb = kb / 1024.0
    val gb = mb / 1024.0
    
    return when {
        gb >= 1 -> String.format("%.1f GB", gb)
        mb >= 1 -> String.format("%.1f MB", mb)
        kb >= 1 -> String.format("%.1f KB", kb)
        else -> "$bytes B"
    }
}

/**
 * 创建类型枚举
 */
enum class CreateType {
    FILE, FOLDER
}

package ai.aiot.model

import kotlinx.serialization.Serializable
import java.io.File

/**
 * 文件项数据模型
 */
@Serializable
data class FileItem(
    val name: String,
    val path: String,
    val isDirectory: Boolean,
    val size: Long = 0L,
    val lastModified: Long = 0L,
    val children: MutableList<FileItem> = mutableListOf(),
    var isExpanded: Boolean = false
) {
    companion object {
        fun fromFile(file: File): FileItem {
            return FileItem(
                name = file.name,
                path = file.absolutePath,
                isDirectory = file.isDirectory,
                size = if (file.isFile) file.length() else 0L,
                lastModified = file.lastModified()
            )
        }
    }
    
    /**
     * 获取文件扩展名
     */
    fun getExtension(): String {
        return if (isDirectory) "" else name.substringAfterLast('.', "")
    }
    
    /**
     * 判断是否为代码文件
     */
    fun isCodeFile(): Boolean {
        val codeExtensions = setOf(
            "kt", "java", "xml", "json", "gradle", "kts",
            "js", "ts", "html", "css", "py", "cpp", "c", "h"
        )
        return codeExtensions.contains(getExtension().lowercase())
    }
    
    /**
     * 获取文件图标类型
     */
    fun getFileType(): FileType {
        return when {
            isDirectory -> FileType.FOLDER
            getExtension().lowercase() in setOf("kt", "java") -> FileType.CODE_KOTLIN
            getExtension().lowercase() == "xml" -> FileType.CODE_XML
            getExtension().lowercase() == "json" -> FileType.CODE_JSON
            getExtension().lowercase() in setOf("gradle", "kts") -> FileType.CODE_GRADLE
            isCodeFile() -> FileType.CODE_GENERIC
            else -> FileType.FILE_GENERIC
        }
    }
}

/**
 * 文件类型枚举
 */
enum class FileType {
    FOLDER,
    FILE_GENERIC,
    CODE_KOTLIN,
    CODE_XML,
    CODE_JSON,
    CODE_GRADLE,
    CODE_GENERIC
}

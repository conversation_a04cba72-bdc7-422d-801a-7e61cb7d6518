# AI编程IDE 部署指南

## 🎉 项目构建成功！

您的AI编程IDE Android应用已经成功编译，APK文件位于：
```
app/build/outputs/apk/debug/app-debug.apk
```

## 📱 安装到设备的方法

### 方法一：使用自动安装脚本（推荐）

#### 1. USB连接安装
```bash
# 简单安装脚本（推荐新手使用）
python install_ai_ide.py

# 功能完整的安装脚本
python quick_install.py
```

#### 2. 无线安装（Android 11+）
```bash
# 配对设备
python quick_install.py --pair-only --ip <设备IP地址>

# 安装APK
python quick_install.py --ip <设备IP地址> --apk app/build/outputs/apk/debug/app-debug.apk
```

### 方法二：手动安装

#### 1. 通过ADB命令
```bash
# 安装到连接的设备
adb install -r app/build/outputs/apk/debug/app-debug.apk

# 安装到指定设备
adb -s <设备ID> install -r app/build/outputs/apk/debug/app-debug.apk
```

#### 2. 通过文件传输
1. 将APK文件复制到手机
2. 在手机上打开文件管理器
3. 找到APK文件并点击安装

## 🔧 设备准备

### Android设备设置
1. **开启开发者选项**
   - 设置 → 关于手机 → 连续点击"版本号"7次

2. **开启USB调试**
   - 设置 → 开发者选项 → USB调试

3. **授权电脑调试**
   - 连接USB后，手机会弹出授权提示
   - 勾选"始终允许"并确认

### 无线调试设置（Android 11+）
1. **开启无线调试**
   - 设置 → 开发者选项 → 无线调试

2. **获取IP地址**
   - 设置 → WLAN → 点击已连接的网络查看IP

3. **配对设备**
   - 无线调试 → 使用配对码配对设备
   - 记录IP地址、端口和配对码

## 📋 安装脚本功能对比

| 功能 | install_ai_ide.py | quick_install.py |
|------|------------------|------------------|
| 自动构建 | ✅ | ❌ |
| USB安装 | ✅ | ✅ |
| 无线安装 | ❌ | ✅ |
| 设备配对 | ❌ | ✅ |
| 多端口尝试 | ❌ | ✅ |
| 强制安装 | ✅ | ✅ |
| 适合用户 | 新手 | 高级用户 |

## 🚀 快速开始

### 最简单的方式
1. 连接Android设备到电脑（USB线）
2. 运行命令：
   ```bash
   python install_ai_ide.py
   ```
3. 按提示操作即可

### 无线安装方式
1. 确保手机和电脑在同一WiFi网络
2. 获取手机IP地址
3. 运行命令：
   ```bash
   python quick_install.py --ip <手机IP地址> --apk app/build/outputs/apk/debug/app-debug.apk
   ```

## 🔍 故障排除

### 常见问题

#### 1. ADB工具未找到
**解决方案：**
- 安装Android Studio
- 或下载独立的ADB工具
- 确保adb命令在系统PATH中

#### 2. 设备未检测到
**解决方案：**
- 检查USB连接
- 确认已开启USB调试
- 重新授权电脑调试权限

#### 3. 安装失败：签名不匹配
**解决方案：**
- 卸载手机上的旧版本应用
- 或使用强制安装参数：`-r -d -t`

#### 4. 无线连接失败
**解决方案：**
- 确认手机和电脑在同一网络
- 检查防火墙设置
- 尝试重新配对设备

### 获取详细日志
```bash
# 查看ADB设备
adb devices -l

# 查看安装日志
adb logcat | grep -i install
```

## 📱 应用功能

安装成功后，您的AI编程IDE将包含以下功能：

### 核心功能
- 📁 **项目管理** - 多文件夹项目导入
- 🔐 **权限控制** - 精细的AI编辑权限
- 📊 **文件分析** - 智能项目结构分析
- 🤖 **AI助手** - 多种AI服务支持
- ✏️ **代码编辑** - 16进制行号系统

### 技术特性
- **文件层次结构** - 自定义格式存储
- **AI API配置** - 支持OpenAI、Claude、Gemini等
- **实时同步** - 文件变化实时更新
- **智能提示** - AI驱动的代码建议

## 🎯 下一步

1. **启动应用** - 在手机上找到"AI编程IDE"图标
2. **创建项目** - 导入您的代码文件夹
3. **配置AI** - 设置您的AI API密钥
4. **开始编程** - 享受AI增强的编程体验！

---

**🎉 恭喜！您的AI编程IDE已经准备就绪！**

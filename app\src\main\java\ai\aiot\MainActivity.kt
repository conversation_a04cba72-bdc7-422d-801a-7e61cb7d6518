package ai.aiot

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import ai.aiot.ui.screens.IDEMainScreen
import ai.aiot.ui.screens.PermissionScreen
import ai.aiot.ui.theme.AIotTheme
import ai.aiot.utils.PermissionUtils

class MainActivity : ComponentActivity() {

    private lateinit var permissionUtils: PermissionUtils

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        permissionUtils = PermissionUtils(this)

        setContent {
            AIotTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    var hasPermissions by remember { mutableStateOf(permissionUtils.hasStoragePermission()) }

                    if (hasPermissions) {
                        IDEMainScreen()
                    } else {
                        PermissionScreen(
                            onRequestPermission = {
                                permissionUtils.requestStoragePermission(
                                    onGranted = { hasPermissions = true },
                                    onDenied = { /* 可以显示错误消息或退出应用 */ }
                                )
                            }
                        )
                    }
                }
            }
        }
    }
}
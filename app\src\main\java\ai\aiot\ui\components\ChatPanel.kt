package ai.aiot.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import ai.aiot.model.ChatMessage
import ai.aiot.model.MessageType
import ai.aiot.viewmodel.ChatViewModel
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*

/**
 * AI聊天面板组件
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ChatPanel(
    viewModel: ChatViewModel,
    modifier: Modifier = Modifier
) {
    val messages by viewModel.messages.collectAsState()
    val isTyping by viewModel.isTyping.collectAsState()
    val currentInput by viewModel.currentInput.collectAsState()
    val errorMessage by viewModel.errorMessage.collectAsState()
    val pendingOperation by viewModel.pendingOperation.collectAsState()
    
    val listState = rememberLazyListState()
    val coroutineScope = rememberCoroutineScope()
    
    // 自动滚动到最新消息
    LaunchedEffect(messages.size) {
        if (messages.isNotEmpty()) {
            coroutineScope.launch {
                listState.animateScrollToItem(messages.size - 1)
            }
        }
    }
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.surface)
    ) {
        // 聊天标题栏
        ChatHeader(
            onClearChat = { viewModel.clearChat() }
        )
        
        Divider()
        
        // 消息列表
        Box(modifier = Modifier.weight(1f)) {
            LazyColumn(
                state = listState,
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(8.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(messages) { message ->
                    MessageItem(
                        message = message,
                        onCopyMessage = { messageId ->
                            viewModel.copyMessage(messageId)
                        },
                        onDeleteMessage = { messageId ->
                            viewModel.deleteMessage(messageId)
                        },
                        onRegenerateResponse = { messageId ->
                            viewModel.regenerateResponse(messageId)
                        }
                    )
                }
                
                // 显示AI正在输入的指示器
                if (isTyping) {
                    item {
                        TypingIndicator()
                    }
                }
            }
        }
        
        // 错误消息
        val currentError = errorMessage
        if (currentError != null) {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(8.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.errorContainer
                )
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(12.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Error,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.onErrorContainer
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = currentError,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onErrorContainer,
                        modifier = Modifier.weight(1f)
                    )
                    IconButton(onClick = { viewModel.clearError() }) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "关闭",
                            tint = MaterialTheme.colorScheme.onErrorContainer
                        )
                    }
                }
            }
        }
        
        Divider()
        
        // 输入区域
        ChatInputArea(
            currentInput = currentInput,
            onInputChange = { viewModel.updateInput(it) },
            onSendMessage = { viewModel.sendMessage(it) },
            isEnabled = !isTyping
        )
    }

    // 操作确认对话框
    val currentPendingOperation = pendingOperation
    if (currentPendingOperation != null) {
        AlertDialog(
            onDismissRequest = { viewModel.cancelPendingOperation() },
            title = { Text("确认操作") },
            text = {
                Column {
                    Text("AI请求执行以下操作：")
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = currentPendingOperation.generatePrompt(),
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier
                            .background(
                                MaterialTheme.colorScheme.surfaceVariant,
                                RoundedCornerShape(8.dp)
                            )
                            .padding(12.dp)
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "⚠️ 此操作可能会修改您的文件，请确认是否继续。",
                        color = MaterialTheme.colorScheme.error,
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            },
            confirmButton = {
                Button(
                    onClick = { viewModel.confirmPendingOperation() }
                ) {
                    Text("确认执行")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { viewModel.cancelPendingOperation() }
                ) {
                    Text("取消")
                }
            }
        )
    }
}

/**
 * 聊天标题栏
 */
@Composable
private fun ChatHeader(
    onClearChat: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(8.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            Icon(
                imageVector = Icons.Default.SmartToy,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "AI助手",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
        }
        
        IconButton(onClick = onClearChat) {
            Icon(
                imageVector = Icons.Default.ClearAll,
                contentDescription = "清空聊天"
            )
        }
    }
}

/**
 * 消息项组件
 */
@Composable
private fun MessageItem(
    message: ChatMessage,
    onCopyMessage: (String) -> Unit,
    onDeleteMessage: (String) -> Unit,
    onRegenerateResponse: (String) -> Unit
) {
    val isUser = message.isFromUser
    val alignment = if (isUser) Alignment.CenterEnd else Alignment.CenterStart
    
    Box(
        modifier = Modifier.fillMaxWidth(),
        contentAlignment = alignment
    ) {
        Card(
            modifier = Modifier
                .widthIn(max = 280.dp)
                .clip(RoundedCornerShape(12.dp)),
            colors = CardDefaults.cardColors(
                containerColor = when {
                    isUser -> MaterialTheme.colorScheme.primary
                    message.messageType == MessageType.ERROR -> MaterialTheme.colorScheme.errorContainer
                    message.messageType == MessageType.SYSTEM -> MaterialTheme.colorScheme.secondaryContainer
                    else -> MaterialTheme.colorScheme.surfaceVariant
                }
            )
        ) {
            Column(
                modifier = Modifier.padding(12.dp)
            ) {
                // 消息内容
                Text(
                    text = message.content,
                    style = MaterialTheme.typography.bodyMedium,
                    color = when {
                        isUser -> MaterialTheme.colorScheme.onPrimary
                        message.messageType == MessageType.ERROR -> MaterialTheme.colorScheme.onErrorContainer
                        message.messageType == MessageType.SYSTEM -> MaterialTheme.colorScheme.onSecondaryContainer
                        else -> MaterialTheme.colorScheme.onSurfaceVariant
                    }
                )
                
                // 代码片段
                message.codeSnippet?.let { codeSnippet ->
                    Spacer(modifier = Modifier.height(8.dp))
                    CodeSnippetCard(codeSnippet = codeSnippet)
                }
                
                // 时间戳
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = formatTimestamp(message.timestamp),
                    style = MaterialTheme.typography.bodySmall,
                    color = when {
                        isUser -> MaterialTheme.colorScheme.onPrimary.copy(alpha = 0.7f)
                        message.messageType == MessageType.ERROR -> MaterialTheme.colorScheme.onErrorContainer.copy(alpha = 0.7f)
                        message.messageType == MessageType.SYSTEM -> MaterialTheme.colorScheme.onSecondaryContainer.copy(alpha = 0.7f)
                        else -> MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
                    },
                    textAlign = if (isUser) TextAlign.End else TextAlign.Start,
                    modifier = Modifier.fillMaxWidth()
                )
                
                // 操作按钮（仅对AI消息显示）
                if (!isUser && message.messageType != MessageType.SYSTEM) {
                    Spacer(modifier = Modifier.height(8.dp))
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        IconButton(
                            onClick = { onCopyMessage(message.id) },
                            modifier = Modifier.size(32.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.ContentCopy,
                                contentDescription = "复制",
                                modifier = Modifier.size(16.dp)
                            )
                        }
                        IconButton(
                            onClick = { onRegenerateResponse(message.id) },
                            modifier = Modifier.size(32.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Refresh,
                                contentDescription = "重新生成",
                                modifier = Modifier.size(16.dp)
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 代码片段卡片
 */
@Composable
private fun CodeSnippetCard(
    codeSnippet: ai.aiot.model.CodeSnippet
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Column(
            modifier = Modifier.padding(8.dp)
        ) {
            // 代码头部信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = codeSnippet.fileName ?: codeSnippet.language,
                    style = MaterialTheme.typography.bodySmall,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )
                IconButton(
                    onClick = { /* TODO: 复制代码 */ },
                    modifier = Modifier.size(24.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.ContentCopy,
                        contentDescription = "复制代码",
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
            
            Divider()
            
            // 代码内容
            Text(
                text = codeSnippet.code,
                style = MaterialTheme.typography.bodySmall.copy(
                    fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace
                ),
                color = MaterialTheme.colorScheme.onSurface,
                modifier = Modifier.padding(top = 8.dp)
            )
        }
    }
}

/**
 * 正在输入指示器
 */
@Composable
private fun TypingIndicator() {
    Box(
        modifier = Modifier.fillMaxWidth(),
        contentAlignment = Alignment.CenterStart
    ) {
        Card(
            modifier = Modifier
                .clip(RoundedCornerShape(12.dp)),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Row(
                modifier = Modifier.padding(12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "AI正在思考",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Spacer(modifier = Modifier.width(8.dp))
                CircularProgressIndicator(
                    modifier = Modifier.size(16.dp),
                    strokeWidth = 2.dp
                )
            }
        }
    }
}

/**
 * 聊天输入区域
 */
@Composable
private fun ChatInputArea(
    currentInput: String,
    onInputChange: (String) -> Unit,
    onSendMessage: (String) -> Unit,
    isEnabled: Boolean
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(8.dp),
        verticalAlignment = Alignment.Bottom
    ) {
        OutlinedTextField(
            value = currentInput,
            onValueChange = onInputChange,
            placeholder = { Text("输入你的问题...") },
            enabled = isEnabled,
            keyboardOptions = KeyboardOptions(
                imeAction = ImeAction.Send
            ),
            keyboardActions = KeyboardActions(
                onSend = {
                    if (currentInput.trim().isNotEmpty()) {
                        onSendMessage(currentInput)
                    }
                }
            ),
            modifier = Modifier.weight(1f),
            maxLines = 4
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        FloatingActionButton(
            onClick = {
                if (currentInput.trim().isNotEmpty()) {
                    onSendMessage(currentInput)
                }
            },

            modifier = Modifier.size(48.dp)
        ) {
            Icon(
                imageVector = Icons.Default.Send,
                contentDescription = "发送"
            )
        }
    }
}

/**
 * 格式化时间戳
 */
private fun formatTimestamp(timestamp: Long): String {
    val sdf = SimpleDateFormat("HH:mm", Locale.getDefault())
    return sdf.format(Date(timestamp))
}

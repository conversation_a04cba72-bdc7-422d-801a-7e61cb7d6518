[{"merged": "ai.aiot.app-merged_res-51:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "ai.aiot.app-main-53:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "ai.aiot.app-merged_res-51:/drawable_ic_launcher_background.xml.flat", "source": "ai.aiot.app-main-53:/drawable/ic_launcher_background.xml"}, {"merged": "ai.aiot.app-merged_res-51:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "ai.aiot.app-main-53:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "ai.aiot.app-merged_res-51:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "ai.aiot.app-main-53:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "ai.aiot.app-merged_res-51:/mipmap-hdpi_ic_launcher.webp.flat", "source": "ai.aiot.app-main-53:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "ai.aiot.app-merged_res-51:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "ai.aiot.app-main-53:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "ai.aiot.app-merged_res-51:/mipmap-anydpi_ic_launcher_round.xml.flat", "source": "ai.aiot.app-main-53:/mipmap-anydpi/ic_launcher_round.xml"}, {"merged": "ai.aiot.app-merged_res-51:/mipmap-anydpi_ic_launcher.xml.flat", "source": "ai.aiot.app-main-53:/mipmap-anydpi/ic_launcher.xml"}, {"merged": "ai.aiot.app-merged_res-51:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "ai.aiot.app-main-53:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "ai.aiot.app-merged_res-51:/mipmap-mdpi_ic_launcher.webp.flat", "source": "ai.aiot.app-main-53:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "ai.aiot.app-merged_res-51:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "ai.aiot.app-main-53:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "ai.aiot.app-merged_res-51:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "ai.aiot.app-main-53:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "ai.aiot.app-merged_res-51:/xml_backup_rules.xml.flat", "source": "ai.aiot.app-main-53:/xml/backup_rules.xml"}, {"merged": "ai.aiot.app-merged_res-51:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "ai.aiot.app-main-53:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "ai.aiot.app-merged_res-51:/xml_data_extraction_rules.xml.flat", "source": "ai.aiot.app-main-53:/xml/data_extraction_rules.xml"}, {"merged": "ai.aiot.app-merged_res-51:/drawable_ic_launcher_foreground.xml.flat", "source": "ai.aiot.app-main-53:/drawable/ic_launcher_foreground.xml"}]
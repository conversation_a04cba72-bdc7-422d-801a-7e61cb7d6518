package ai.aiot.model

import kotlinx.serialization.Serializable
import java.io.File

/**
 * 项目数据模型
 */
@Serializable
data class Project(
    val id: String,
    val name: String,
    val rootPath: String,
    val description: String = "",
    val createdTime: Long = System.currentTimeMillis(),
    val lastAccessTime: Long = System.currentTimeMillis(),
    val isAIEditable: Boolean = true,
    val hierarchyFilePath: String = "", // 层次结构文件路径
    val folders: List<ProjectFolder> = emptyList()
) {
    /**
     * 获取项目显示名称
     */
    fun getDisplayName(): String {
        return if (name.isNotBlank()) name else File(rootPath).name
    }
    
    /**
     * 检查项目路径是否存在
     */
    fun exists(): Boolean {
        return File(rootPath).exists()
    }
    
    /**
     * 获取项目大小（文件数量）
     */
    fun getFileCount(): Int {
        return folders.sumOf { it.getFileCount() }
    }
}

/**
 * 项目文件夹模型
 */
@Serializable
data class ProjectFolder(
    val path: String,
    val name: String,
    val isAIEditable: Boolean = true,
    val includeSubfolders: Boolean = true,
    val excludePatterns: List<String> = listOf(
        "*.class", "*.jar", "*.war", "*.ear",
        "node_modules", ".git", ".svn", ".hg",
        "build", "target", "dist", "out",
        "*.log", "*.tmp", "*.cache"
    ),
    val addedTime: Long = System.currentTimeMillis()
) {
    /**
     * 获取文件夹显示名称
     */
    fun getDisplayName(): String {
        return if (name.isNotBlank()) name else File(path).name
    }
    
    /**
     * 检查路径是否存在
     */
    fun exists(): Boolean {
        return File(path).exists() && File(path).isDirectory
    }
    
    /**
     * 获取文件数量
     */
    fun getFileCount(): Int {
        return try {
            val folder = File(path)
            if (!folder.exists() || !folder.isDirectory) return 0
            
            var count = 0
            folder.walkTopDown()
                .filter { it.isFile }
                .filter { file -> !isExcluded(file.name) }
                .forEach { count++ }
            count
        } catch (e: Exception) {
            0
        }
    }
    
    /**
     * 检查文件是否被排除
     */
    private fun isExcluded(fileName: String): Boolean {
        return excludePatterns.any { pattern ->
            when {
                pattern.startsWith("*.") -> fileName.endsWith(pattern.substring(1))
                pattern.contains("*") -> {
                    val regex = pattern.replace("*", ".*").toRegex()
                    regex.matches(fileName)
                }
                else -> fileName == pattern || fileName.contains(pattern)
            }
        }
    }
}

/**
 * 文件层次结构模型
 */
@Serializable
data class FileHierarchy(
    val projectId: String,
    val hierarchyString: String, // 格式: <A><2\a><3\a.txt>|<B>
    val generatedTime: Long = System.currentTimeMillis(),
    val totalFiles: Int = 0,
    val totalFolders: Int = 0
) {
    /**
     * 解析层次结构字符串
     */
    fun parseHierarchy(): List<HierarchyNode> {
        val nodes = mutableListOf<HierarchyNode>()
        val parts = hierarchyString.split("|")
        
        parts.forEach { part ->
            if (part.isNotBlank()) {
                nodes.addAll(parseHierarchyPart(part))
            }
        }
        
        return nodes
    }
    
    private fun parseHierarchyPart(part: String): List<HierarchyNode> {
        val nodes = mutableListOf<HierarchyNode>()
        val regex = "<(\\d*)(\\\\|\\?)?([^<>]+)>".toRegex()
        val matches = regex.findAll(part)
        
        matches.forEach { match ->
            val level = if (match.groupValues[1].isEmpty()) 1 else match.groupValues[1].toInt()
            val type = when (match.groupValues[2]) {
                "\\" -> NodeType.FOLDER
                "?" -> NodeType.FILE
                else -> NodeType.FOLDER
            }
            val name = match.groupValues[3]
            
            nodes.add(HierarchyNode(name, type, level))
        }
        
        return nodes
    }
}

/**
 * 层次节点
 */
data class HierarchyNode(
    val name: String,
    val type: NodeType,
    val level: Int
)

/**
 * 节点类型
 */
enum class NodeType {
    FILE, FOLDER
}

/**
 * AI编辑权限
 */
@Serializable
data class AIEditPermission(
    val projectId: String,
    val allowedPaths: List<String> = emptyList(),
    val deniedPaths: List<String> = emptyList(),
    val allowedExtensions: List<String> = listOf(
        "kt", "java", "xml", "json", "gradle", "kts",
        "js", "ts", "html", "css", "py", "cpp", "c", "h",
        "md", "txt", "yml", "yaml", "properties"
    ),
    val maxFileSize: Long = 1024 * 1024, // 1MB
    val requireConfirmation: Boolean = true
) {
    /**
     * 检查文件是否允许AI编辑
     */
    fun canAIEdit(filePath: String): Boolean {
        val file = File(filePath)
        
        // 检查文件大小
        if (file.length() > maxFileSize) return false
        
        // 检查扩展名
        val extension = file.extension.lowercase()
        if (extension.isNotEmpty() && !allowedExtensions.contains(extension)) return false
        
        // 检查拒绝路径
        if (deniedPaths.any { filePath.contains(it) }) return false
        
        // 检查允许路径（如果有指定）
        if (allowedPaths.isNotEmpty()) {
            return allowedPaths.any { filePath.contains(it) }
        }
        
        return true
    }
}

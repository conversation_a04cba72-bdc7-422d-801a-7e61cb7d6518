package ai.aiot.utils

import android.content.Context
import java.io.File

/**
 * 示例项目创建器
 */
object SampleProjectCreator {
    
    /**
     * 在应用专用目录创建示例项目
     */
    fun createSampleProject(context: Context): String? {
        return try {
            val projectDir = File(context.getExternalFilesDir(null), "SampleProject")
            if (!projectDir.exists()) {
                projectDir.mkdirs()
            }
            
            // 创建主要的Kotlin文件
            createMainKotlinFile(projectDir)
            
            // 创建数据类文件
            createDataClassFile(projectDir)
            
            // 创建工具类文件
            createUtilsFile(projectDir)
            
            // 创建README文件
            createReadmeFile(projectDir)
            
            // 创建配置文件
            createConfigFile(projectDir)
            
            projectDir.absolutePath
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
    
    private fun createMainKotlinFile(projectDir: File) {
        val mainFile = File(projectDir, "MainActivity.kt")
        mainFile.writeText("""
            package com.example.sampleproject
            
            import android.os.Bundle
            import androidx.activity.ComponentActivity
            import androidx.activity.compose.setContent
            import androidx.compose.foundation.layout.*
            import androidx.compose.material3.*
            import androidx.compose.runtime.*
            import androidx.compose.ui.Alignment
            import androidx.compose.ui.Modifier
            import androidx.compose.ui.unit.dp
            
            class MainActivity : ComponentActivity() {
                override fun onCreate(savedInstanceState: Bundle?) {
                    super.onCreate(savedInstanceState)
                    setContent {
                        SampleTheme {
                            MainScreen()
                        }
                    }
                }
            }
            
            @Composable
            fun MainScreen() {
                var counter by remember { mutableStateOf(0) }
                
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Text(
                        text = "计数器: ${'$'}counter",
                        style = MaterialTheme.typography.headlineMedium
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Button(onClick = { counter++ }) {
                            Text("增加")
                        }
                        
                        Button(onClick = { counter-- }) {
                            Text("减少")
                        }
                        
                        Button(onClick = { counter = 0 }) {
                            Text("重置")
                        }
                    }
                }
            }
            
            @Composable
            fun SampleTheme(content: @Composable () -> Unit) {
                MaterialTheme {
                    content()
                }
            }
        """.trimIndent())
    }
    
    private fun createDataClassFile(projectDir: File) {
        val dataFile = File(projectDir, "User.kt")
        dataFile.writeText("""
            package com.example.sampleproject
            
            import kotlinx.serialization.Serializable
            
            /**
             * 用户数据类
             */
            @Serializable
            data class User(
                val id: Long,
                val name: String,
                val email: String,
                val age: Int,
                val isActive: Boolean = true
            ) {
                /**
                 * 获取用户显示名称
                 */
                fun getDisplayName(): String {
                    return if (name.isNotBlank()) name else "未知用户"
                }
                
                /**
                 * 检查用户是否成年
                 */
                fun isAdult(): Boolean {
                    return age >= 18
                }
                
                /**
                 * 获取用户状态描述
                 */
                fun getStatusDescription(): String {
                    return if (isActive) "活跃" else "非活跃"
                }
            }
            
            /**
             * 用户管理器
             */
            class UserManager {
                private val users = mutableListOf<User>()
                
                fun addUser(user: User) {
                    users.add(user)
                }
                
                fun removeUser(userId: Long) {
                    users.removeAll { it.id == userId }
                }
                
                fun findUserById(id: Long): User? {
                    return users.find { it.id == id }
                }
                
                fun getActiveUsers(): List<User> {
                    return users.filter { it.isActive }
                }
                
                fun getUserCount(): Int {
                    return users.size
                }
            }
        """.trimIndent())
    }
    
    private fun createUtilsFile(projectDir: File) {
        val utilsFile = File(projectDir, "Utils.kt")
        utilsFile.writeText("""
            package com.example.sampleproject
            
            import java.text.SimpleDateFormat
            import java.util.*
            
            /**
             * 工具类集合
             */
            object Utils {
                
                /**
                 * 格式化时间戳
                 */
                fun formatTimestamp(timestamp: Long, pattern: String = "yyyy-MM-dd HH:mm:ss"): String {
                    val sdf = SimpleDateFormat(pattern, Locale.getDefault())
                    return sdf.format(Date(timestamp))
                }
                
                /**
                 * 验证邮箱格式
                 */
                fun isValidEmail(email: String): Boolean {
                    val emailPattern = "[a-zA-Z0-9._-]+@[a-z]+\\.+[a-z]+"
                    return email.matches(emailPattern.toRegex())
                }
                
                /**
                 * 生成随机字符串
                 */
                fun generateRandomString(length: Int): String {
                    val chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
                    return (1..length)
                        .map { chars.random() }
                        .joinToString("")
                }
                
                /**
                 * 计算两个日期之间的天数差
                 */
                fun daysBetween(startDate: Date, endDate: Date): Long {
                    val diffInMillis = endDate.time - startDate.time
                    return diffInMillis / (1000 * 60 * 60 * 24)
                }
                
                /**
                 * 将字节数转换为可读格式
                 */
                fun formatFileSize(bytes: Long): String {
                    val kb = bytes / 1024.0
                    val mb = kb / 1024.0
                    val gb = mb / 1024.0
                    
                    return when {
                        gb >= 1 -> String.format("%.2f GB", gb)
                        mb >= 1 -> String.format("%.2f MB", mb)
                        kb >= 1 -> String.format("%.2f KB", kb)
                        else -> "${'$'}bytes B"
                    }
                }
            }
            
            /**
             * 扩展函数
             */
            fun String.isValidPhoneNumber(): Boolean {
                val phonePattern = "^[+]?[0-9]{10,13}${'$'}"
                return this.matches(phonePattern.toRegex())
            }
            
            fun List<Int>.average(): Double {
                return if (this.isEmpty()) 0.0 else this.sum().toDouble() / this.size
            }
        """.trimIndent())
    }
    
    private fun createReadmeFile(projectDir: File) {
        val readmeFile = File(projectDir, "README.md")
        readmeFile.writeText("""
            # 示例项目
            
            这是一个由AI编程IDE创建的示例项目，展示了基本的Android开发结构。
            
            ## 项目结构
            
            - `MainActivity.kt` - 主活动，包含一个简单的计数器界面
            - `User.kt` - 用户数据类和用户管理器
            - `Utils.kt` - 常用工具函数集合
            - `config.json` - 项目配置文件
            
            ## 功能特性
            
            ### MainActivity
            - 使用Jetpack Compose构建UI
            - 实现简单的计数器功能
            - 响应式状态管理
            
            ### User类
            - 数据类定义
            - 用户验证方法
            - 用户管理功能
            
            ### Utils工具类
            - 时间格式化
            - 邮箱验证
            - 随机字符串生成
            - 文件大小格式化
            - 扩展函数示例
            
            ## 使用说明
            
            1. 在AI编程IDE中打开此项目
            2. 浏览不同的文件了解代码结构
            3. 使用AI助手获取代码解释和改进建议
            4. 尝试修改代码并保存
            
            ## 技术栈
            
            - Kotlin
            - Android Jetpack Compose
            - Material Design 3
            
            ---
            
            *此项目由AI编程IDE自动生成*
        """.trimIndent())
    }
    
    private fun createConfigFile(projectDir: File) {
        val configFile = File(projectDir, "config.json")
        configFile.writeText("""
            {
              "project": {
                "name": "SampleProject",
                "version": "1.0.0",
                "description": "AI编程IDE示例项目",
                "author": "AI Assistant",
                "created": "${System.currentTimeMillis()}"
              },
              "settings": {
                "theme": "light",
                "language": "kotlin",
                "autoSave": true,
                "showLineNumbers": true,
                "tabSize": 4
              },
              "dependencies": [
                "androidx.compose.ui:ui",
                "androidx.compose.material3:material3",
                "androidx.activity:activity-compose"
              ]
            }
        """.trimIndent())
    }
}

package ai.aiot.viewmodel

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import ai.aiot.model.Project
import ai.aiot.model.ProjectFolder
import ai.aiot.model.FileHierarchy
import ai.aiot.model.AIEditPermission
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.json.Json
import java.io.File
import java.util.UUID

/**
 * 项目管理ViewModel
 */
class ProjectManagerViewModel : ViewModel() {
    
    private val _projects = MutableStateFlow<List<Project>>(emptyList())
    val projects: StateFlow<List<Project>> = _projects.asStateFlow()
    
    private val _currentProject = MutableStateFlow<Project?>(null)
    val currentProject: StateFlow<Project?> = _currentProject.asStateFlow()
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    private val _loadingProgress = MutableStateFlow(0f)
    val loadingProgress: StateFlow<Float> = _loadingProgress.asStateFlow()
    
    private val _loadingMessage = MutableStateFlow("")
    val loadingMessage: StateFlow<String> = _loadingMessage.asStateFlow()
    
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()
    
    private val json = Json { 
        prettyPrint = true
        ignoreUnknownKeys = true
    }
    
    /**
     * 初始化项目管理器
     */
    fun initialize(context: Context) {
        viewModelScope.launch {
            loadProjects(context)
        }
    }
    
    /**
     * 创建新项目
     */
    fun createProject(
        context: Context,
        name: String,
        rootPath: String,
        description: String = "",
        folders: List<ProjectFolder> = emptyList()
    ) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _loadingMessage.value = "创建项目..."
                
                val project = Project(
                    id = UUID.randomUUID().toString(),
                    name = name,
                    rootPath = rootPath,
                    description = description,
                    folders = folders
                )
                
                // 保存项目
                saveProject(context, project)
                
                // 生成文件层次结构
                generateFileHierarchy(context, project)
                
                // 更新项目列表
                val updatedProjects = _projects.value + project
                _projects.value = updatedProjects
                _currentProject.value = project
                
                saveProjectsList(context, updatedProjects)
                
            } catch (e: Exception) {
                _errorMessage.value = "创建项目失败: ${e.message}"
            } finally {
                _isLoading.value = false
                _loadingMessage.value = ""
                _loadingProgress.value = 0f
            }
        }
    }
    
    /**
     * 添加文件夹到项目
     */
    fun addFolderToProject(
        context: Context,
        projectId: String,
        folder: ProjectFolder
    ) {
        viewModelScope.launch {
            try {
                val project = _projects.value.find { it.id == projectId } ?: return@launch
                val updatedProject = project.copy(
                    folders = project.folders + folder,
                    lastAccessTime = System.currentTimeMillis()
                )
                
                updateProject(context, updatedProject)
                
                // 重新生成文件层次结构
                generateFileHierarchy(context, updatedProject)
                
            } catch (e: Exception) {
                _errorMessage.value = "添加文件夹失败: ${e.message}"
            }
        }
    }
    
    /**
     * 选择项目
     */
    fun selectProject(project: Project) {
        _currentProject.value = project.copy(lastAccessTime = System.currentTimeMillis())
    }
    
    /**
     * 生成文件层次结构
     */
    private suspend fun generateFileHierarchy(context: Context, project: Project) {
        try {
            _loadingMessage.value = "分析文件结构..."
            _loadingProgress.value = 0.1f
            
            val hierarchyBuilder = StringBuilder()
            var totalFiles = 0
            var totalFolders = 0
            
            project.folders.forEachIndexed { folderIndex, folder ->
                if (folderIndex > 0) hierarchyBuilder.append("|")
                
                _loadingMessage.value = "分析文件夹: ${folder.getDisplayName()}"
                _loadingProgress.value = 0.1f + (folderIndex.toFloat() / project.folders.size) * 0.8f
                
                val folderResult = generateFolderHierarchy(folder, 1)
                hierarchyBuilder.append(folderResult.hierarchy)
                totalFiles += folderResult.fileCount
                totalFolders += folderResult.folderCount
            }
            
            val hierarchy = FileHierarchy(
                projectId = project.id,
                hierarchyString = hierarchyBuilder.toString(),
                totalFiles = totalFiles,
                totalFolders = totalFolders
            )
            
            // 保存层次结构到隐藏文件
            saveFileHierarchy(context, hierarchy)
            
            _loadingMessage.value = "文件结构分析完成"
            _loadingProgress.value = 1.0f
            
        } catch (e: Exception) {
            _errorMessage.value = "生成文件层次结构失败: ${e.message}"
        }
    }
    
    /**
     * 生成文件夹层次结构
     */
    private fun generateFolderHierarchy(folder: ProjectFolder, level: Int): HierarchyResult {
        val folderFile = File(folder.path)
        if (!folderFile.exists() || !folderFile.isDirectory) {
            return HierarchyResult("", 0, 0)
        }
        
        val builder = StringBuilder()
        var fileCount = 0
        var folderCount = 1 // 当前文件夹
        
        // 添加当前文件夹
        val levelPrefix = if (level == 1) "" else level.toString()
        builder.append("<$levelPrefix\\${folderFile.name}>")
        
        try {
            val children = folderFile.listFiles()?.sortedWith(compareBy<File> { !it.isDirectory }.thenBy { it.name })
            
            children?.forEach { child ->
                if (child.isDirectory && folder.includeSubfolders) {
                    val childFolder = folder.copy(path = child.absolutePath)
                    val childResult = generateFolderHierarchy(childFolder, level + 1)
                    builder.append(childResult.hierarchy)
                    fileCount += childResult.fileCount
                    folderCount += childResult.folderCount
                } else if (child.isFile && !isExcluded(child.name, folder.excludePatterns)) {
                    val childLevelPrefix = if (level + 1 == 1) "" else (level + 1).toString()
                    builder.append("<$childLevelPrefix?${child.name}>")
                    fileCount++
                }
            }
        } catch (e: Exception) {
            // 忽略权限错误等
        }
        
        return HierarchyResult(builder.toString(), fileCount, folderCount)
    }
    
    /**
     * 检查文件是否被排除
     */
    private fun isExcluded(fileName: String, excludePatterns: List<String>): Boolean {
        return excludePatterns.any { pattern ->
            when {
                pattern.startsWith("*.") -> fileName.endsWith(pattern.substring(1))
                pattern.contains("*") -> {
                    val regex = pattern.replace("*", ".*").toRegex()
                    regex.matches(fileName)
                }
                else -> fileName == pattern || fileName.contains(pattern)
            }
        }
    }
    
    /**
     * 保存文件层次结构
     */
    private fun saveFileHierarchy(context: Context, hierarchy: FileHierarchy) {
        try {
            val hierarchyDir = File(context.filesDir, ".ai_hierarchy")
            if (!hierarchyDir.exists()) hierarchyDir.mkdirs()
            
            val hierarchyFile = File(hierarchyDir, "${hierarchy.projectId}.json")
            val jsonString = json.encodeToString(hierarchy)
            hierarchyFile.writeText(jsonString)
            
        } catch (e: Exception) {
            throw Exception("保存文件层次结构失败: ${e.message}")
        }
    }
    
    /**
     * 加载项目列表
     */
    private fun loadProjects(context: Context) {
        try {
            val projectsFile = File(context.filesDir, "projects.json")
            if (projectsFile.exists()) {
                val jsonString = projectsFile.readText()
                val projects = json.decodeFromString<List<Project>>(jsonString)
                _projects.value = projects.filter { it.exists() }
            }
        } catch (e: Exception) {
            _errorMessage.value = "加载项目列表失败: ${e.message}"
        }
    }
    
    /**
     * 保存项目列表
     */
    private fun saveProjectsList(context: Context, projects: List<Project>) {
        try {
            val projectsFile = File(context.filesDir, "projects.json")
            val jsonString = json.encodeToString(projects)
            projectsFile.writeText(jsonString)
        } catch (e: Exception) {
            throw Exception("保存项目列表失败: ${e.message}")
        }
    }
    
    /**
     * 保存单个项目
     */
    private fun saveProject(context: Context, project: Project) {
        try {
            val projectDir = File(context.filesDir, "projects")
            if (!projectDir.exists()) projectDir.mkdirs()
            
            val projectFile = File(projectDir, "${project.id}.json")
            val jsonString = json.encodeToString(project)
            projectFile.writeText(jsonString)
        } catch (e: Exception) {
            throw Exception("保存项目失败: ${e.message}")
        }
    }
    
    /**
     * 更新项目
     */
    private fun updateProject(context: Context, project: Project) {
        val updatedProjects = _projects.value.map { 
            if (it.id == project.id) project else it 
        }
        _projects.value = updatedProjects
        
        if (_currentProject.value?.id == project.id) {
            _currentProject.value = project
        }
        
        saveProject(context, project)
        saveProjectsList(context, updatedProjects)
    }
    
    /**
     * 获取文件层次结构
     */
    fun getFileHierarchy(context: Context, projectId: String): FileHierarchy? {
        return try {
            val hierarchyFile = File(context.filesDir, ".ai_hierarchy/$projectId.json")
            if (hierarchyFile.exists()) {
                val jsonString = hierarchyFile.readText()
                json.decodeFromString<FileHierarchy>(jsonString)
            } else null
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 清除错误消息
     */
    fun clearError() {
        _errorMessage.value = null
    }
    
    /**
     * 层次结构生成结果
     */
    private data class HierarchyResult(
        val hierarchy: String,
        val fileCount: Int,
        val folderCount: Int
    )
}

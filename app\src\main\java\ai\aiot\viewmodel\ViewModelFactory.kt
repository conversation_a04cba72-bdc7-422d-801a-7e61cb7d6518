package ai.aiot.viewmodel

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import ai.aiot.service.AIOperationService
import ai.aiot.service.AIService

/**
 * ViewModel工厂
 * 用于创建需要依赖注入的ViewModel实例
 */
class ViewModelFactory(
    private val context: Context
) : ViewModelProvider.Factory {
    
    private val aiService = AIService()
    private val aiOperationService = AIOperationService(context)
    
    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return when (modelClass) {
            ChatViewModel::class.java -> {
                ChatViewModel(context, aiService, aiOperationService) as T
            }
            else -> throw IllegalArgumentException("Unknown ViewModel class: ${modelClass.name}")
        }
    }
}

/**
 * 创建ChatViewModel的便捷函数
 */
fun createChatViewModel(context: Context): ChatViewModel {
    return ChatViewModel(context, AIService(), AIOperationService(context))
}

package ai.aiot.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import ai.aiot.ui.components.*
import ai.aiot.ui.screens.ProjectSelectionScreen
import ai.aiot.ui.screens.AIConfigScreen
import ai.aiot.viewmodel.*
import ai.aiot.model.Project
import ai.aiot.model.AIConfig

/**
 * IDE主界面屏幕
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun IDEMainScreen() {
    val context = LocalContext.current

    // ViewModels
    val projectManagerViewModel: ProjectManagerViewModel = viewModel()
    val fileManagerViewModel: FileManagerViewModel = viewModel()
    val codeEditorViewModel: CodeEditorViewModel = viewModel()
    val chatViewModel = remember { createChatViewModel(context) }

    // 状态
    val currentProject by projectManagerViewModel.currentProject.collectAsState()
    val isProjectLoading by projectManagerViewModel.isLoading.collectAsState()
    val loadingProgress by projectManagerViewModel.loadingProgress.collectAsState()
    val loadingMessage by projectManagerViewModel.loadingMessage.collectAsState()

    // 界面状态
    var selectedPanel by remember { mutableStateOf(IDEPanel.FILE_MANAGER) }
    var showChatPanel by remember { mutableStateOf(false) }
    var showAIConfig by remember { mutableStateOf(false) }

    // 设置ChatViewModel的项目路径
    LaunchedEffect(currentProject) {
        currentProject?.let { project ->
            chatViewModel.setCurrentProjectPath(project.rootPath)
        }
    }
    
    // 根据项目状态显示不同界面
    when {
        currentProject == null -> {
            // 显示项目选择界面
            ProjectSelectionScreen(
                viewModel = projectManagerViewModel,
                onProjectSelected = { project ->
                    projectManagerViewModel.selectProject(project)

                    // 加载AI配置
                    codeEditorViewModel.loadAIConfig(context)

                    // 获取文件层次结构并传递给AI
                    val hierarchy = projectManagerViewModel.getFileHierarchy(context, project.id)
                    hierarchy?.let {
                        chatViewModel.setFileHierarchy(it.hierarchyString)
                    }
                }
            )
        }

        showAIConfig -> {
            // 显示AI配置界面
            val aiConfig by codeEditorViewModel.aiConfig.collectAsState()
            AIConfigScreen(
                config = aiConfig ?: AIConfig(),
                onConfigChanged = { config ->
                    codeEditorViewModel.saveAIConfig(context, config)
                    showAIConfig = false
                },
                onBack = { showAIConfig = false }
            )
        }

        else -> {
            // 显示主IDE界面
            val project = currentProject!!
            IDEWorkspace(
                project = project,
                selectedPanel = selectedPanel,
                showChatPanel = showChatPanel,
                isProjectLoading = isProjectLoading,
                loadingProgress = loadingProgress,
                loadingMessage = loadingMessage,
                fileManagerViewModel = fileManagerViewModel,
                codeEditorViewModel = codeEditorViewModel,
                chatViewModel = chatViewModel,
                onPanelSelected = { panel ->
                    selectedPanel = panel
                    if (panel == IDEPanel.CHAT) {
                        showChatPanel = true
                    }
                },
                onShowAIConfig = { showAIConfig = true },
                onToggleChatPanel = { showChatPanel = !showChatPanel }
            )
        }
    }
}

/**
 * IDE工作区
 */
@Composable
private fun IDEWorkspace(
    project: Project,
    selectedPanel: IDEPanel,
    showChatPanel: Boolean,
    isProjectLoading: Boolean,
    loadingProgress: Float,
    loadingMessage: String,
    fileManagerViewModel: FileManagerViewModel,
    codeEditorViewModel: CodeEditorViewModel,
    chatViewModel: ChatViewModel,
    onPanelSelected: (IDEPanel) -> Unit,
    onShowAIConfig: () -> Unit,
    onToggleChatPanel: () -> Unit
) {
    Row(modifier = Modifier.fillMaxSize()) {
        // 左侧导航栏
        NavigationRail(
            selectedPanel = selectedPanel,
            onPanelSelected = onPanelSelected,
            onShowAIConfig = onShowAIConfig,
            modifier = Modifier.fillMaxHeight()
        )
        
        // 主内容区域
        Box(modifier = Modifier.weight(1f)) {
            Row(modifier = Modifier.fillMaxSize()) {
                // 左侧面板
                if (selectedPanel == IDEPanel.FILE_MANAGER || selectedPanel == IDEPanel.CODE_EDITOR) {
                    Card(
                        modifier = Modifier
                            .width(300.dp)
                            .fillMaxHeight(),
                        shape = RectangleShape
                    ) {
                        Column {
                            // 文件管理器
                            FileManagerPanel(
                                viewModel = fileManagerViewModel,
                                onFileSelected = { fileItem ->
                                    if (!fileItem.isDirectory && fileItem.isCodeFile()) {
                                        codeEditorViewModel.openFile(fileItem.path)
                                        onPanelSelected(IDEPanel.CODE_EDITOR)
                                    }
                                },
                                modifier = Modifier.weight(1f)
                            )

                            // AI编辑建议面板
                            val aiEditRequests by codeEditorViewModel.aiEditRequests.collectAsState()
                            if (aiEditRequests.isNotEmpty()) {
                                Divider()
                                AIEditSuggestionPanel(
                                    suggestions = aiEditRequests,
                                    onApply = { codeEditorViewModel.applyAIEdit(it) },
                                    onReject = { codeEditorViewModel.rejectAIEdit(it) },
                                    modifier = Modifier.heightIn(max = 200.dp)
                                )
                            }
                        }
                    }
                }
                
                // 中央内容区域
                Box(modifier = Modifier.weight(1f)) {
                    when (selectedPanel) {
                        IDEPanel.FILE_MANAGER -> {
                            ProjectWelcomeScreen(project = project)
                        }
                        IDEPanel.CODE_EDITOR -> {
                            CodeEditorPanel(viewModel = codeEditorViewModel)
                        }
                        IDEPanel.CHAT -> {
                            Column {
                                // 文件分析面板
                                val context = androidx.compose.ui.platform.LocalContext.current
                                val fileHierarchy = remember(project.id) {
                                    // 这里需要传递projectManagerViewModel实例
                                    null // 临时修复，稍后完善
                                }

                                FileAnalysisPanel(
                                    isLoading = isProjectLoading,
                                    loadingProgress = loadingProgress,
                                    loadingMessage = loadingMessage,
                                    fileHierarchy = fileHierarchy,
                                    onAnalysisComplete = { hierarchy ->
                                        chatViewModel.setFileHierarchy(hierarchy)
                                    },
                                    modifier = Modifier.padding(16.dp)
                                )

                                // 聊天面板
                                ChatPanel(
                                    viewModel = chatViewModel,
                                    modifier = Modifier.weight(1f)
                                )
                            }
                        }
                    }
                }
                
                // 右侧聊天面板（可选显示）
                if (showChatPanel && selectedPanel != IDEPanel.CHAT) {
                    Card(
                        modifier = Modifier
                            .width(350.dp)
                            .fillMaxHeight(),
                        shape = RectangleShape
                    ) {
                        Column {
                            // 聊天面板标题栏
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(8.dp),
                                horizontalArrangement = Arrangement.SpaceBetween
                            ) {
                                Text(
                                    text = "AI助手",
                                    style = MaterialTheme.typography.titleMedium
                                )
                                IconButton(
                                    onClick = onToggleChatPanel
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Close,
                                        contentDescription = "关闭聊天面板"
                                    )
                                }
                            }
                            Divider()
                            ChatPanel(
                                viewModel = chatViewModel,
                                modifier = Modifier.weight(1f)
                            )
                        }
                    }
                }
            }
        }
    }
    

}

/**
 * 左侧导航栏
 */
@Composable
private fun NavigationRail(
    selectedPanel: IDEPanel,
    onPanelSelected: (IDEPanel) -> Unit,
    onShowAIConfig: () -> Unit,
    modifier: Modifier = Modifier
) {
    NavigationRail(
        modifier = modifier.background(MaterialTheme.colorScheme.surfaceVariant),
        containerColor = MaterialTheme.colorScheme.surfaceVariant
    ) {
        Spacer(modifier = Modifier.height(16.dp))

        // 文件管理器
        NavigationRailItem(
            icon = {
                Icon(
                    imageVector = Icons.Default.Folder,
                    contentDescription = "文件管理器"
                )
            },
            label = { Text("文件") },
            selected = selectedPanel == IDEPanel.FILE_MANAGER,
            onClick = { onPanelSelected(IDEPanel.FILE_MANAGER) }
        )

        // 代码编辑器
        NavigationRailItem(
            icon = {
                Icon(
                    imageVector = Icons.Default.Code,
                    contentDescription = "代码编辑器"
                )
            },
            label = { Text("编辑器") },
            selected = selectedPanel == IDEPanel.CODE_EDITOR,
            onClick = { onPanelSelected(IDEPanel.CODE_EDITOR) }
        )

        // AI聊天
        NavigationRailItem(
            icon = {
                Icon(
                    imageVector = Icons.Default.SmartToy,
                    contentDescription = "AI助手"
                )
            },
            label = { Text("AI助手") },
            selected = selectedPanel == IDEPanel.CHAT,
            onClick = { onPanelSelected(IDEPanel.CHAT) }
        )

        Spacer(modifier = Modifier.weight(1f))

        // AI配置
        NavigationRailItem(
            icon = {
                Icon(
                    imageVector = Icons.Default.Psychology,
                    contentDescription = "AI配置"
                )
            },
            label = { Text("AI配置") },
            selected = false,
            onClick = onShowAIConfig
        )

        // 设置
        NavigationRailItem(
            icon = {
                Icon(
                    imageVector = Icons.Default.Settings,
                    contentDescription = "设置"
                )
            },
            label = { Text("设置") },
            selected = false,
            onClick = { /* TODO: 打开设置 */ }
        )
    }
}

/**
 * 项目欢迎屏幕
 */
@Composable
private fun ProjectWelcomeScreen(project: Project) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background),
        contentAlignment = androidx.compose.ui.Alignment.Center
    ) {
        Column(
            horizontalAlignment = androidx.compose.ui.Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp),
            modifier = Modifier.padding(32.dp)
        ) {
            Icon(
                imageVector = Icons.Default.Code,
                contentDescription = null,
                modifier = Modifier.size(64.dp),
                tint = MaterialTheme.colorScheme.primary
            )

            Text(
                text = "欢迎回到",
                style = MaterialTheme.typography.headlineSmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            Text(
                text = project.getDisplayName(),
                style = MaterialTheme.typography.headlineMedium,
                color = MaterialTheme.colorScheme.onBackground
            )

            if (project.description.isNotBlank()) {
                Text(
                    text = project.description,
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 项目统计
            Row(
                horizontalArrangement = Arrangement.spacedBy(24.dp)
            ) {
                ProjectStat(
                    icon = Icons.Default.Folder,
                    count = project.folders.size,
                    label = "文件夹"
                )

                ProjectStat(
                    icon = Icons.Default.InsertDriveFile,
                    count = project.getFileCount(),
                    label = "文件"
                )

                ProjectStat(
                    icon = Icons.Default.SmartToy,
                    count = if (project.isAIEditable) 1 else 0,
                    label = "AI可编辑"
                )
            }

            Spacer(modifier = Modifier.height(24.dp))

            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        text = "快速开始：",
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onSurface
                    )

                    FeatureItem(
                        icon = Icons.Default.Folder,
                        text = "浏览左侧文件管理器中的文件"
                    )

                    FeatureItem(
                        icon = Icons.Default.Code,
                        text = "点击代码文件开始编辑"
                    )

                    FeatureItem(
                        icon = Icons.Default.SmartToy,
                        text = "使用AI助手获取编程帮助"
                    )

                    FeatureItem(
                        icon = Icons.Default.AutoFixHigh,
                        text = "选择代码请求AI优化建议"
                    )
                }
            }
        }
    }
}

/**
 * 项目统计组件
 */
@Composable
private fun ProjectStat(
    icon: ImageVector,
    count: Int,
    label: String
) {
    Column(
        horizontalAlignment = androidx.compose.ui.Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            modifier = Modifier.size(24.dp),
            tint = MaterialTheme.colorScheme.primary
        )
        Text(
            text = count.toString(),
            style = MaterialTheme.typography.titleLarge,
            color = MaterialTheme.colorScheme.onSurface
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * 功能特性项
 */
@Composable
private fun FeatureItem(
    icon: ImageVector,
    text: String
) {
    Row(
        verticalAlignment = androidx.compose.ui.Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            modifier = Modifier.size(20.dp),
            tint = MaterialTheme.colorScheme.primary
        )
        Text(
            text = text,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurface
        )
    }
}

/**
 * IDE面板枚举
 */
enum class IDEPanel {
    FILE_MANAGER,
    CODE_EDITOR,
    CHAT
}

package ai.aiot.ui.components

import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import ai.aiot.model.ProjectFolder
import java.io.File

/**
 * 创建项目对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CreateProjectDialog(
    onConfirm: (String, String, String, List<ProjectFolder>) -> Unit,
    onDismiss: () -> Unit
) {
    var projectName by remember { mutableStateOf("") }
    var projectDescription by remember { mutableStateOf("") }
    var rootPath by remember { mutableStateOf("") }
    var folders by remember { mutableStateOf<List<ProjectFolder>>(emptyList()) }
    var showFolderDialog by remember { mutableStateOf(false) }
    
    val folderPickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.OpenDocumentTree()
    ) { uri ->
        uri?.let {
            // 在实际应用中，这里需要处理URI到路径的转换
            // 这里简化处理
            val path = uri.path ?: ""
            if (path.isNotEmpty()) {
                rootPath = path
            }
        }
    }
    
    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // 标题
                Text(
                    text = "创建新项目",
                    style = MaterialTheme.typography.headlineSmall
                )
                
                // 项目名称
                OutlinedTextField(
                    value = projectName,
                    onValueChange = { projectName = it },
                    label = { Text("项目名称") },
                    placeholder = { Text("输入项目名称") },
                    modifier = Modifier.fillMaxWidth(),
                    keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next),
                    singleLine = true
                )
                
                // 项目描述
                OutlinedTextField(
                    value = projectDescription,
                    onValueChange = { projectDescription = it },
                    label = { Text("项目描述（可选）") },
                    placeholder = { Text("简要描述项目内容") },
                    modifier = Modifier.fillMaxWidth(),
                    keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next),
                    maxLines = 3
                )
                
                // 根路径选择
                OutlinedTextField(
                    value = rootPath,
                    onValueChange = { rootPath = it },
                    label = { Text("项目根路径") },
                    placeholder = { Text("选择项目根目录") },
                    modifier = Modifier.fillMaxWidth(),
                    trailingIcon = {
                        IconButton(onClick = { folderPickerLauncher.launch(null) }) {
                            Icon(
                                imageVector = Icons.Default.FolderOpen,
                                contentDescription = "选择文件夹"
                            )
                        }
                    },
                    readOnly = true
                )
                
                // 文件夹列表
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surfaceVariant
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "包含的文件夹",
                                style = MaterialTheme.typography.titleSmall
                            )
                            
                            TextButton(
                                onClick = { showFolderDialog = true }
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Add,
                                    contentDescription = null,
                                    modifier = Modifier.size(16.dp)
                                )
                                Spacer(modifier = Modifier.width(4.dp))
                                Text("添加")
                            }
                        }
                        
                        if (folders.isEmpty()) {
                            Text(
                                text = "还没有添加文件夹",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant,
                                modifier = Modifier.padding(vertical = 8.dp)
                            )
                        } else {
                            LazyColumn(
                                modifier = Modifier.heightIn(max = 200.dp),
                                verticalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                items(folders) { folder ->
                                    FolderItem(
                                        folder = folder,
                                        onRemove = {
                                            folders = folders.filter { it.path != folder.path }
                                        }
                                    )
                                }
                            }
                        }
                    }
                }
                
                // 按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    TextButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("取消")
                    }
                    
                    Button(
                        onClick = {
                            if (projectName.isNotBlank() && rootPath.isNotBlank()) {
                                onConfirm(projectName, rootPath, projectDescription, folders)
                            }
                        },
                        enabled = projectName.isNotBlank() && rootPath.isNotBlank(),
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("创建")
                    }
                }
            }
        }
    }
    
    // 添加文件夹对话框
    if (showFolderDialog) {
        AddFolderDialog(
            onConfirm = { folder ->
                folders = folders + folder
                showFolderDialog = false
            },
            onDismiss = { showFolderDialog = false }
        )
    }
}

/**
 * 文件夹项
 */
@Composable
private fun FolderItem(
    folder: ProjectFolder,
    onRemove: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .background(
                MaterialTheme.colorScheme.surface,
                RoundedCornerShape(8.dp)
            )
            .padding(12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = Icons.Default.Folder,
            contentDescription = null,
            modifier = Modifier.size(20.dp),
            tint = MaterialTheme.colorScheme.primary
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = folder.getDisplayName(),
                style = MaterialTheme.typography.bodyMedium,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            Text(
                text = folder.path,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }
        
        Row {
            if (folder.isAIEditable) {
                Icon(
                    imageVector = Icons.Default.SmartToy,
                    contentDescription = "AI可编辑",
                    modifier = Modifier.size(16.dp),
                    tint = MaterialTheme.colorScheme.primary
                )
                Spacer(modifier = Modifier.width(4.dp))
            }
            
            IconButton(
                onClick = onRemove,
                modifier = Modifier.size(32.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = "移除",
                    modifier = Modifier.size(16.dp)
                )
            }
        }
    }
}

/**
 * 添加文件夹对话框
 */
@Composable
private fun AddFolderDialog(
    onConfirm: (ProjectFolder) -> Unit,
    onDismiss: () -> Unit
) {
    var folderPath by remember { mutableStateOf("") }
    var folderName by remember { mutableStateOf("") }
    var isAIEditable by remember { mutableStateOf(true) }
    var includeSubfolders by remember { mutableStateOf(true) }
    
    val folderPickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.OpenDocumentTree()
    ) { uri ->
        uri?.let {
            val path = uri.path ?: ""
            if (path.isNotEmpty()) {
                folderPath = path
                folderName = File(path).name
            }
        }
    }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("添加文件夹") },
        text = {
            Column(
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                OutlinedTextField(
                    value = folderPath,
                    onValueChange = { folderPath = it },
                    label = { Text("文件夹路径") },
                    placeholder = { Text("选择文件夹") },
                    modifier = Modifier.fillMaxWidth(),
                    trailingIcon = {
                        IconButton(onClick = { folderPickerLauncher.launch(null) }) {
                            Icon(
                                imageVector = Icons.Default.FolderOpen,
                                contentDescription = "选择文件夹"
                            )
                        }
                    },
                    readOnly = true
                )
                
                OutlinedTextField(
                    value = folderName,
                    onValueChange = { folderName = it },
                    label = { Text("显示名称") },
                    placeholder = { Text("文件夹显示名称") },
                    modifier = Modifier.fillMaxWidth()
                )
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Checkbox(
                        checked = isAIEditable,
                        onCheckedChange = { isAIEditable = it }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("允许AI编辑")
                }
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Checkbox(
                        checked = includeSubfolders,
                        onCheckedChange = { includeSubfolders = it }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("包含子文件夹")
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    if (folderPath.isNotBlank()) {
                        val folder = ProjectFolder(
                            path = folderPath,
                            name = folderName.ifBlank { File(folderPath).name },
                            isAIEditable = isAIEditable,
                            includeSubfolders = includeSubfolders
                        )
                        onConfirm(folder)
                    }
                },
                enabled = folderPath.isNotBlank()
            ) {
                Text("添加")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

package ai.aiot

import android.os.Bundle
import android.view.WindowManager
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import ai.aiot.ui.screens.IDEMainScreen
import ai.aiot.ui.screens.PermissionScreen
import ai.aiot.ui.theme.AIotTheme
import ai.aiot.utils.PermissionUtils

class MainActivity : ComponentActivity() {

    private lateinit var permissionUtils: PermissionUtils

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // 隐藏状态栏和导航栏
        hideSystemUI()

        permissionUtils = PermissionUtils(this)

        setContent {
            AIotTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    var hasPermissions by remember { mutableStateOf(permissionUtils.hasStoragePermission()) }

                    if (hasPermissions) {
                        IDEMainScreen()
                    } else {
                        PermissionScreen(
                            onRequestPermission = {
                                permissionUtils.requestStoragePermission(
                                    onGranted = { hasPermissions = true },
                                    onDenied = { /* 可以显示错误消息或退出应用 */ }
                                )
                            }
                        )
                    }
                }
            }
        }
    }

    private fun hideSystemUI() {
        // 保持屏幕常亮
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

        // 获取窗口控制器
        val windowInsetsController = WindowCompat.getInsetsController(window, window.decorView)

        // 隐藏状态栏和导航栏
        windowInsetsController.hide(WindowInsetsCompat.Type.systemBars())

        // 设置系统栏行为
        windowInsetsController.systemBarsBehavior =
            WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
    }

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
        if (hasFocus) {
            hideSystemUI()
        }
    }
}
package ai.aiot.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import ai.aiot.model.EditorState
import ai.aiot.viewmodel.CodeEditorViewModel

/**
 * 代码编辑器面板组件
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CodeEditorPanel(
    viewModel: CodeEditorViewModel,
    modifier: Modifier = Modifier
) {
    val editorState by viewModel.editorState.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val errorMessage by viewModel.errorMessage.collectAsState()
    
    var textFieldValue by remember { mutableStateOf(TextFieldValue()) }
    
    // 同步编辑器状态到文本字段
    LaunchedEffect(editorState.content) {
        if (textFieldValue.text != editorState.content) {
            textFieldValue = TextFieldValue(
                text = editorState.content,
                selection = textFieldValue.selection
            )
        }
    }
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.surface)
    ) {
        // 编辑器工具栏
        CodeEditorToolbar(
            editorState = editorState,
            onSave = { viewModel.saveFile() },
            onUndo = { viewModel.undo() },
            onRedo = { viewModel.redo() },
            onFormat = { viewModel.formatCode() }
        )
        
        Divider()
        
        // 编辑器内容区域
        Box(modifier = Modifier.fillMaxSize()) {
            when {
                isLoading -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator()
                    }
                }
                
                errorMessage != null -> {
                    ErrorMessage(
                        message = errorMessage,
                        onDismiss = { viewModel.clearError() }
                    )
                }
                
                editorState.fileName.isEmpty() -> {
                    EmptyEditor()
                }
                
                else -> {
                    CodeEditor(
                        editorState = editorState,
                        textFieldValue = textFieldValue,
                        onTextChange = { newValue ->
                            textFieldValue = newValue
                            viewModel.updateContent(
                                newValue.text,
                                newValue.selection.start
                            )
                        },
                        onCursorPositionChange = { position ->
                            viewModel.setCursorPosition(position)
                        }
                    )
                }
            }
        }
    }
}

/**
 * 代码编辑器工具栏
 */
@Composable
private fun CodeEditorToolbar(
    editorState: EditorState,
    onSave: () -> Unit,
    onUndo: () -> Unit,
    onRedo: () -> Unit,
    onFormat: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(8.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 文件信息
        Column {
            Text(
                text = if (editorState.fileName.isNotEmpty()) {
                    editorState.fileName + if (editorState.isModified) " *" else ""
                } else {
                    "未打开文件"
                },
                style = MaterialTheme.typography.titleMedium
            )
            if (editorState.fileName.isNotEmpty()) {
                Text(
                    text = "行 ${editorState.currentLine}, 列 ${editorState.currentColumn} | ${editorState.language}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
        
        // 工具按钮
        Row {
            IconButton(
                onClick = onUndo,
                enabled = editorState.fileName.isNotEmpty()
            ) {
                Icon(
                    imageVector = Icons.Default.Undo,
                    contentDescription = "撤销"
                )
            }
            IconButton(
                onClick = onRedo,
                enabled = editorState.fileName.isNotEmpty()
            ) {
                Icon(
                    imageVector = Icons.Default.Redo,
                    contentDescription = "重做"
                )
            }
            IconButton(
                onClick = onFormat,
                enabled = editorState.fileName.isNotEmpty()
            ) {
                Icon(
                    imageVector = Icons.Default.FormatAlignLeft,
                    contentDescription = "格式化"
                )
            }
            IconButton(
                onClick = onSave,
                enabled = editorState.fileName.isNotEmpty() && editorState.isModified
            ) {
                Icon(
                    imageVector = Icons.Default.Save,
                    contentDescription = "保存"
                )
            }
        }
    }
}

/**
 * 代码编辑器主体
 */
@Composable
private fun CodeEditor(
    editorState: EditorState,
    textFieldValue: TextFieldValue,
    onTextChange: (TextFieldValue) -> Unit,
    onCursorPositionChange: (Int) -> Unit
) {
    val horizontalScrollState = rememberScrollState()
    val verticalScrollState = rememberScrollState()
    
    Row(modifier = Modifier.fillMaxSize()) {
        // 行号区域
        LineNumberColumn(
            lineNumbers = editorState.lineNumbers,
            currentLine = editorState.currentLine,
            modifier = Modifier
                .verticalScroll(verticalScrollState)
                .background(MaterialTheme.colorScheme.surfaceVariant)
        )
        
        // 代码编辑区域
        Box(
            modifier = Modifier
                .weight(1f)
                .fillMaxHeight()
        ) {
            BasicTextField(
                value = textFieldValue,
                onValueChange = { newValue ->
                    onTextChange(newValue)
                    onCursorPositionChange(newValue.selection.start)
                },
                textStyle = TextStyle(
                    fontFamily = FontFamily.Monospace,
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurface
                ),
                keyboardOptions = KeyboardOptions(
                    imeAction = ImeAction.Default
                ),
                modifier = Modifier
                    .fillMaxSize()
                    .padding(8.dp)
                    .horizontalScroll(horizontalScrollState)
                    .verticalScroll(verticalScrollState)
            )
        }
    }
}

/**
 * 行号列
 */
@Composable
private fun LineNumberColumn(
    lineNumbers: List<Int>,
    currentLine: Int,
    modifier: Modifier = Modifier
) {
    val lineHeight = 20.dp
    
    Column(
        modifier = modifier
            .width(48.dp)
            .drawBehind {
                // 绘制右边框
                drawLine(
                    color = Color.Gray.copy(alpha = 0.3f),
                    start = Offset(size.width, 0f),
                    end = Offset(size.width, size.height),
                    strokeWidth = 1.dp.toPx()
                )
            }
    ) {
        lineNumbers.forEach { lineNumber ->
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(lineHeight)
                    .background(
                        if (lineNumber == currentLine) {
                            MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
                        } else {
                            Color.Transparent
                        }
                    ),
                contentAlignment = Alignment.CenterEnd
            ) {
                Text(
                    text = lineNumber.toString(),
                    style = MaterialTheme.typography.bodySmall.copy(
                        fontFamily = FontFamily.Monospace,
                        fontSize = 12.sp
                    ),
                    color = if (lineNumber == currentLine) {
                        MaterialTheme.colorScheme.primary
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    },
                    modifier = Modifier.padding(end = 4.dp)
                )
            }
        }
    }
}

/**
 * 空编辑器提示
 */
@Composable
private fun EmptyEditor() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.Code,
                contentDescription = null,
                modifier = Modifier.size(48.dp),
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "选择一个文件开始编辑",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "从左侧文件管理器中选择要编辑的文件",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * 错误消息组件
 */
@Composable
private fun ErrorMessage(
    message: String,
    onDismiss: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.errorContainer
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.Error,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onErrorContainer
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = message,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onErrorContainer,
                modifier = Modifier.weight(1f)
            )
            IconButton(onClick = onDismiss) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = "关闭",
                    tint = MaterialTheme.colorScheme.onErrorContainer
                )
            }
        }
    }
}

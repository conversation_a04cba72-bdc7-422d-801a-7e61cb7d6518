package ai.aiot.viewmodel

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import ai.aiot.model.*
import ai.aiot.service.AIService
import ai.aiot.utils.FileHierarchyManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.json.Json
import java.io.File

/**
 * 代码编辑器ViewModel
 */
class CodeEditorViewModel : ViewModel() {
    
    private val _editorState = MutableStateFlow(EditorState())
    val editorState: StateFlow<EditorState> = _editorState.asStateFlow()
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    // AI编辑相关状态
    private val _aiEditRequests = MutableStateFlow<List<CodeEditResponse>>(emptyList())
    val aiEditRequests: StateFlow<List<CodeEditResponse>> = _aiEditRequests.asStateFlow()

    private val _isAIProcessing = MutableStateFlow(false)
    val isAIProcessing: StateFlow<Boolean> = _isAIProcessing.asStateFlow()

    private val _aiConfig = MutableStateFlow<AIConfig?>(null)
    val aiConfig: StateFlow<AIConfig?> = _aiConfig.asStateFlow()

    // 操作历史记录
    private val undoStack = mutableListOf<EditorOperation>()
    private val redoStack = mutableListOf<EditorOperation>()
    private val maxHistorySize = 100

    private val json = Json {
        prettyPrint = true
        ignoreUnknownKeys = true
    }
    
    /**
     * 打开文件
     */
    fun openFile(filePath: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _errorMessage.value = null
                
                val file = File(filePath)
                if (!file.exists() || !file.isFile) {
                    _errorMessage.value = "文件不存在"
                    return@launch
                }
                
                val content = file.readText()
                val fileName = file.name
                val language = detectLanguage(fileName)
                
                _editorState.value = EditorState(
                    content = content,
                    fileName = fileName,
                    filePath = filePath,
                    language = language,
                    isModified = false,
                    lineNumbers = generateLineNumbers(content)
                )
                
                // 清空历史记录
                undoStack.clear()
                redoStack.clear()
                
            } catch (e: Exception) {
                _errorMessage.value = "打开文件失败: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 保存文件
     */
    fun saveFile() {
        viewModelScope.launch {
            try {
                val state = _editorState.value
                if (state.filePath.isEmpty()) {
                    _errorMessage.value = "没有打开的文件"
                    return@launch
                }
                
                val file = File(state.filePath)
                file.writeText(state.content)
                
                _editorState.value = state.copy(isModified = false)
                
            } catch (e: Exception) {
                _errorMessage.value = "保存文件失败: ${e.message}"
            }
        }
    }
    
    /**
     * 另存为
     */
    fun saveAsFile(newFilePath: String) {
        viewModelScope.launch {
            try {
                val state = _editorState.value
                val file = File(newFilePath)
                file.writeText(state.content)
                
                val fileName = file.name
                val language = detectLanguage(fileName)
                
                _editorState.value = state.copy(
                    fileName = fileName,
                    filePath = newFilePath,
                    language = language,
                    isModified = false
                )
                
            } catch (e: Exception) {
                _errorMessage.value = "另存为失败: ${e.message}"
            }
        }
    }
    
    /**
     * 更新编辑器内容
     */
    fun updateContent(newContent: String, cursorPosition: Int = 0) {
        val currentState = _editorState.value
        
        // 记录操作到历史
        val operation = EditorOperation(
            action = EditorAction.REPLACE,
            position = 0,
            oldText = currentState.content,
            newText = newContent
        )
        addToHistory(operation)
        
        val (line, column) = currentState.calculateLineAndColumn(cursorPosition)
        
        _editorState.value = currentState.copy(
            content = newContent,
            cursorPosition = cursorPosition,
            isModified = true,
            lineNumbers = generateLineNumbers(newContent),
            currentLine = line,
            currentColumn = column
        )
    }
    
    /**
     * 插入文本
     */
    fun insertText(text: String, position: Int) {
        val currentState = _editorState.value
        val newContent = StringBuilder(currentState.content).apply {
            insert(position, text)
        }.toString()
        
        // 记录操作到历史
        val operation = EditorOperation(
            action = EditorAction.INSERT,
            position = position,
            oldText = "",
            newText = text
        )
        addToHistory(operation)
        
        updateContent(newContent, position + text.length)
    }
    
    /**
     * 删除文本
     */
    fun deleteText(startPosition: Int, endPosition: Int) {
        val currentState = _editorState.value
        val deletedText = currentState.content.substring(startPosition, endPosition)
        val newContent = StringBuilder(currentState.content).apply {
            delete(startPosition, endPosition)
        }.toString()
        
        // 记录操作到历史
        val operation = EditorOperation(
            action = EditorAction.DELETE,
            position = startPosition,
            oldText = deletedText,
            newText = ""
        )
        addToHistory(operation)
        
        updateContent(newContent, startPosition)
    }
    
    /**
     * 撤销操作
     */
    fun undo() {
        if (undoStack.isNotEmpty()) {
            val operation = undoStack.removeLastOrNull()
            operation?.let {
                redoStack.add(it)
                applyReverseOperation(it)
            }
        }
    }
    
    /**
     * 重做操作
     */
    fun redo() {
        if (redoStack.isNotEmpty()) {
            val operation = redoStack.removeLastOrNull()
            operation?.let {
                undoStack.add(it)
                applyOperation(it)
            }
        }
    }
    
    /**
     * 设置光标位置
     */
    fun setCursorPosition(position: Int) {
        val currentState = _editorState.value
        val (line, column) = currentState.calculateLineAndColumn(position)
        
        _editorState.value = currentState.copy(
            cursorPosition = position,
            currentLine = line,
            currentColumn = column
        )
    }
    
    /**
     * 设置选择范围
     */
    fun setSelection(start: Int, end: Int) {
        val currentState = _editorState.value
        _editorState.value = currentState.copy(
            selectionStart = start,
            selectionEnd = end
        )
    }
    
    /**
     * 格式化代码
     */
    fun formatCode() {
        val currentState = _editorState.value
        // 这里可以集成代码格式化库
        // 目前只是简单的缩进处理
        val formattedContent = formatCodeContent(currentState.content, currentState.language)
        updateContent(formattedContent)
    }
    
    /**
     * 检测编程语言
     */
    private fun detectLanguage(fileName: String): String {
        return when (fileName.substringAfterLast('.', "").lowercase()) {
            "kt" -> "kotlin"
            "java" -> "java"
            "xml" -> "xml"
            "json" -> "json"
            "js" -> "javascript"
            "ts" -> "typescript"
            "html" -> "html"
            "css" -> "css"
            "py" -> "python"
            "cpp", "cc", "cxx" -> "cpp"
            "c" -> "c"
            "h", "hpp" -> "c"
            "gradle", "kts" -> "gradle"
            else -> "text"
        }
    }
    
    /**
     * 生成行号
     */
    private fun generateLineNumbers(content: String): List<Int> {
        val lineCount = content.count { it == '\n' } + 1
        return (1..lineCount).toList()
    }
    
    /**
     * 添加操作到历史记录
     */
    private fun addToHistory(operation: EditorOperation) {
        undoStack.add(operation)
        if (undoStack.size > maxHistorySize) {
            undoStack.removeFirstOrNull()
        }
        redoStack.clear()
    }
    
    /**
     * 应用操作
     */
    private fun applyOperation(operation: EditorOperation) {
        val currentState = _editorState.value
        val newContent = when (operation.action) {
            EditorAction.INSERT -> {
                StringBuilder(currentState.content).apply {
                    insert(operation.position, operation.newText)
                }.toString()
            }
            EditorAction.DELETE -> {
                StringBuilder(currentState.content).apply {
                    delete(operation.position, operation.position + operation.oldText.length)
                }.toString()
            }
            EditorAction.REPLACE -> operation.newText
            else -> currentState.content
        }
        
        _editorState.value = currentState.copy(
            content = newContent,
            isModified = true,
            lineNumbers = generateLineNumbers(newContent)
        )
    }
    
    /**
     * 应用反向操作
     */
    private fun applyReverseOperation(operation: EditorOperation) {
        val currentState = _editorState.value
        val newContent = when (operation.action) {
            EditorAction.INSERT -> {
                StringBuilder(currentState.content).apply {
                    delete(operation.position, operation.position + operation.newText.length)
                }.toString()
            }
            EditorAction.DELETE -> {
                StringBuilder(currentState.content).apply {
                    insert(operation.position, operation.oldText)
                }.toString()
            }
            EditorAction.REPLACE -> operation.oldText
            else -> currentState.content
        }
        
        _editorState.value = currentState.copy(
            content = newContent,
            isModified = true,
            lineNumbers = generateLineNumbers(newContent)
        )
    }
    
    /**
     * 简单的代码格式化
     */
    private fun formatCodeContent(content: String, language: String): String {
        // 这里可以实现更复杂的格式化逻辑
        return content.lines().joinToString("\n") { line ->
            line.trim()
        }
    }
    
    /**
     * 加载AI配置
     */
    fun loadAIConfig(context: Context) {
        viewModelScope.launch {
            try {
                val configFile = File(context.filesDir, "ai_config.json")
                if (configFile.exists()) {
                    val configJson = configFile.readText()
                    val config = json.decodeFromString<AIConfig>(configJson)
                    _aiConfig.value = config
                }
            } catch (e: Exception) {
                _errorMessage.value = "加载AI配置失败: ${e.message}"
            }
        }
    }

    /**
     * 保存AI配置
     */
    fun saveAIConfig(context: Context, config: AIConfig) {
        viewModelScope.launch {
            try {
                val configFile = File(context.filesDir, "ai_config.json")
                val configJson = json.encodeToString(config)
                configFile.writeText(configJson)
                _aiConfig.value = config
            } catch (e: Exception) {
                _errorMessage.value = "保存AI配置失败: ${e.message}"
            }
        }
    }

    /**
     * 请求AI编辑代码
     */
    fun requestAIEdit(
        instruction: String,
        startLine: Int = 0,
        endLine: Int = -1
    ) {
        viewModelScope.launch {
            try {
                val config = _aiConfig.value
                if (config == null || !config.isEnabled) {
                    _errorMessage.value = "AI功能未启用或配置无效"
                    return@launch
                }

                val currentState = _editorState.value
                if (currentState.fileName.isEmpty()) {
                    _errorMessage.value = "没有打开的文件"
                    return@launch
                }

                _isAIProcessing.value = true

                // 确定编辑范围
                val actualStartLine = if (startLine > 0) startLine else 1
                val actualEndLine = if (endLine > 0) endLine else currentState.lineNumbers.size

                // 获取指定范围的代码
                val lines = currentState.content.split('\n')
                val selectedLines = lines.subList(
                    (actualStartLine - 1).coerceAtLeast(0),
                    actualEndLine.coerceAtMost(lines.size)
                )
                val selectedContent = selectedLines.joinToString("\n")

                // 生成16进制行号范围
                val lineRange = FileHierarchyManager.generateLineRangeString(actualStartLine, actualEndLine)

                // 创建编辑请求
                val editRequest = CodeEditRequest(
                    filePath = currentState.filePath,
                    fileName = currentState.fileName,
                    language = currentState.language,
                    content = selectedContent,
                    lineRange = lineRange,
                    instruction = instruction,
                    context = "文件总行数: ${lines.size}, 编辑范围: $actualStartLine-$actualEndLine"
                )

                // 发送AI请求
                val aiService = AIService(config)
                val result = aiService.sendCodeEditRequest(editRequest)

                if (result.isSuccess) {
                    val editResponse = result.getOrThrow()

                    // 添加到编辑请求列表
                    val currentRequests = _aiEditRequests.value
                    _aiEditRequests.value = currentRequests + editResponse

                } else {
                    _errorMessage.value = "AI编辑请求失败: ${result.exceptionOrNull()?.message}"
                }

            } catch (e: Exception) {
                _errorMessage.value = "AI编辑失败: ${e.message}"
            } finally {
                _isAIProcessing.value = false
            }
        }
    }

    /**
     * 应用AI编辑建议
     */
    fun applyAIEdit(editResponse: CodeEditResponse) {
        viewModelScope.launch {
            try {
                val currentState = _editorState.value

                // 解析行号范围
                val lineRangeRegex = "\\[(0x[0-9A-Fa-f]+)-(0x[0-9A-Fa-f]+)\\]".toRegex()
                val match = lineRangeRegex.find(editResponse.lineRange)

                if (match != null) {
                    val startHex = match.groupValues[1]
                    val endHex = match.groupValues[2]

                    val startLine = FileHierarchyManager.parseHexLineNumber(startHex) ?: 1
                    val endLine = FileHierarchyManager.parseHexLineNumber(endHex) ?: 1

                    // 替换指定行的内容
                    val lines = currentState.content.split('\n').toMutableList()
                    val newLines = editResponse.modifiedCode.split('\n')

                    // 删除原有行
                    for (i in (endLine - 1) downTo (startLine - 1)) {
                        if (i < lines.size) {
                            lines.removeAt(i)
                        }
                    }

                    // 插入新行
                    newLines.reversed().forEach { line ->
                        lines.add(startLine - 1, line)
                    }

                    val newContent = lines.joinToString("\n")
                    updateContent(newContent)

                    // 标记为已应用
                    val updatedResponse = editResponse.copy(isApproved = true)
                    val updatedRequests = _aiEditRequests.value.map {
                        if (it.timestamp == editResponse.timestamp) updatedResponse else it
                    }
                    _aiEditRequests.value = updatedRequests

                } else {
                    _errorMessage.value = "无法解析行号范围"
                }

            } catch (e: Exception) {
                _errorMessage.value = "应用AI编辑失败: ${e.message}"
            }
        }
    }

    /**
     * 拒绝AI编辑建议
     */
    fun rejectAIEdit(editResponse: CodeEditResponse) {
        val updatedRequests = _aiEditRequests.value.filter {
            it.timestamp != editResponse.timestamp
        }
        _aiEditRequests.value = updatedRequests
    }

    /**
     * 获取当前选中行的16进制表示
     */
    fun getCurrentLineHex(): String {
        val currentState = _editorState.value
        return FileHierarchyManager.getHexLineNumber(currentState.currentLine)
    }

    /**
     * 获取选中范围的16进制表示
     */
    fun getSelectionRangeHex(): String? {
        val currentState = _editorState.value
        if (currentState.selectionStart == currentState.selectionEnd) return null

        val startLine = currentState.calculateLineAndColumn(currentState.selectionStart).first
        val endLine = currentState.calculateLineAndColumn(currentState.selectionEnd).first

        return FileHierarchyManager.generateLineRangeString(startLine, endLine)
    }

    /**
     * 清除错误消息
     */
    fun clearError() {
        _errorMessage.value = null
    }
}

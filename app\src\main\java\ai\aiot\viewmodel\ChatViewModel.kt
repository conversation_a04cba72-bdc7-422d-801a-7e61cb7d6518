package ai.aiot.viewmodel

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import ai.aiot.model.*
import ai.aiot.service.AIOperationService
import ai.aiot.service.AIService
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import java.util.UUID

/**
 * AI聊天ViewModel
 */
class ChatViewModel(
    private val context: Context,
    private val aiService: AIService,
    private val aiOperationService: AIOperationService
) : ViewModel() {

    private val _messages = MutableStateFlow<List<ChatMessage>>(emptyList())
    val messages: StateFlow<List<ChatMessage>> = _messages.asStateFlow()

    private val _isTyping = MutableStateFlow(false)
    val isTyping: StateFlow<Boolean> = _isTyping.asStateFlow()

    private val _currentInput = MutableStateFlow("")
    val currentInput: StateFlow<String> = _currentInput.asStateFlow()

    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    // 文件层次结构信息
    private val _fileHierarchy = MutableStateFlow<String?>(null)
    val fileHierarchy: StateFlow<String?> = _fileHierarchy.asStateFlow()

    // 对话历史
    private val _conversationHistory = MutableStateFlow(
        ConversationHistory(id = UUID.randomUUID().toString())
    )
    val conversationHistory: StateFlow<ConversationHistory> = _conversationHistory.asStateFlow()

    // 当前项目路径
    private val _currentProjectPath = MutableStateFlow<String?>(null)
    val currentProjectPath: StateFlow<String?> = _currentProjectPath.asStateFlow()

    // AI配置
    private val _aiConfig = MutableStateFlow<AIConfig?>(null)
    val aiConfig: StateFlow<AIConfig?> = _aiConfig.asStateFlow()

    // 待确认的操作
    private val _pendingOperation = MutableStateFlow<AIOperationRequest?>(null)
    val pendingOperation: StateFlow<AIOperationRequest?> = _pendingOperation.asStateFlow()
    
    init {
        // 添加欢迎消息
        addMessage(
            ChatMessage(
                id = UUID.randomUUID().toString(),
                content = "你好！我是你的AI编程助手。我可以帮助你：\n\n" +
                        "• 解答编程问题\n" +
                        "• 生成代码片段\n" +
                        "• 代码审查和优化建议\n" +
                        "• 调试帮助\n" +
                        "• 技术文档解释\n" +
                        "• 文件操作（创建、编辑、删除）\n" +
                        "• 联网搜索信息\n\n" +
                        "有什么我可以帮助你的吗？",
                isFromUser = false,
                messageType = MessageType.SYSTEM
            )
        )
    }

    /**
     * 设置AI配置
     */
    fun setAIConfig(config: AIConfig) {
        _aiConfig.value = config
    }

    /**
     * 设置当前项目路径
     */
    fun setCurrentProjectPath(path: String) {
        _currentProjectPath.value = path
    }
    
    /**
     * 发送消息
     */
    fun sendMessage(content: String) {
        if (content.trim().isEmpty()) return

        viewModelScope.launch {
            try {
                // 添加用户消息
                val userMessage = ChatMessage(
                    id = UUID.randomUUID().toString(),
                    content = content.trim(),
                    isFromUser = true
                )
                addMessage(userMessage)

                // 清空输入
                _currentInput.value = ""

                // 显示AI正在输入
                _isTyping.value = true

                // 检查是否有AI配置
                val config = _aiConfig.value
                if (config == null || !config.isValid()) {
                    // 使用模拟响应
                    val aiResponse = generateAIResponse(content.trim())
                    delay(1000 + (Math.random() * 2000).toLong())

                    val aiMessage = ChatMessage(
                        id = UUID.randomUUID().toString(),
                        content = aiResponse.message,
                        isFromUser = false,
                        messageType = if (aiResponse.codeGenerated != null) MessageType.CODE else MessageType.TEXT,
                        codeSnippet = aiResponse.codeGenerated
                    )
                    addMessage(aiMessage)
                } else {
                    // 使用真实AI API
                    val response = callRealAI(content.trim(), config)

                    val aiMessage = ChatMessage(
                        id = UUID.randomUUID().toString(),
                        content = response,
                        isFromUser = false,
                        messageType = MessageType.AI
                    )
                    addMessage(aiMessage)
                }

            } catch (e: Exception) {
                _errorMessage.value = "发送消息失败: ${e.message}"

                // 添加错误消息
                val errorMessage = ChatMessage(
                    id = UUID.randomUUID().toString(),
                    content = "抱歉，我遇到了一些问题。请稍后再试。",
                    isFromUser = false,
                    messageType = MessageType.ERROR
                )
                addMessage(errorMessage)

            } finally {
                _isTyping.value = false
            }
        }
    }
    
    /**
     * 调用真实AI API
     */
    private suspend fun callRealAI(userInput: String, config: AIConfig): String {
        try {
            // 构建消息历史
            val history = _conversationHistory.value
            val contextMessages = history.getContextMessages()

            // 添加系统提示
            val systemMessage = AIMessage(
                role = "system",
                content = buildSystemPrompt()
            )

            // 添加用户消息
            val userMessage = AIMessage(
                role = "user",
                content = userInput
            )

            val allMessages = listOf(systemMessage) + contextMessages + listOf(userMessage)

            // 构建AI请求
            val request = AIRequest(
                messages = allMessages,
                model = config.model,
                maxTokens = config.maxTokens,
                temperature = config.temperature
            )

            // 调用AI服务
            val response = aiService.sendRequest(request, config)

            if (response.isSuccess()) {
                val aiContent = response.getContent()

                // 检查是否包含操作指令
                val operation = parseAIOperation(aiContent)
                if (operation != null) {
                    handleAIOperation(operation)
                }

                return aiContent
            } else {
                throw Exception(response.error?.message ?: "AI API调用失败")
            }

        } catch (e: Exception) {
            throw Exception("AI API调用失败: ${e.message}")
        }
    }

    /**
     * 构建系统提示
     */
    private fun buildSystemPrompt(): String {
        val projectInfo = _currentProjectPath.value?.let { path ->
            "\n\n当前项目路径: $path"
        } ?: ""

        val hierarchyInfo = _fileHierarchy.value?.let { hierarchy ->
            "\n\n项目文件结构:\n$hierarchy"
        } ?: ""

        return """
你是一个专业的AI编程助手，可以帮助用户进行编程相关的任务。

你具有以下能力：
1. 回答编程问题和提供技术建议
2. 生成和优化代码
3. 文件操作：创建、编辑、删除文件和文件夹
4. 联网搜索最新信息
5. 分析项目结构和代码

当用户需要文件操作时，请使用以下格式：
- 创建文件：[CREATE_FILE:路径:内容]
- 创建文件夹：[CREATE_FOLDER:路径]
- 编辑文件：[EDIT_FILE:路径:新内容]
- 删除文件：[DELETE_FILE:路径]
- 删除文件夹：[DELETE_FOLDER:路径]
- 联网搜索：[WEB_SEARCH:搜索内容]
- 读取文件：[READ_FILE:路径]
- 列出文件：[LIST_FILES:路径]

请始终提供清晰、准确的回答，并在执行危险操作前提醒用户。$projectInfo$hierarchyInfo
        """.trimIndent()
    }

    /**
     * 更新当前输入
     */
    fun updateInput(input: String) {
        _currentInput.value = input
    }
    
    /**
     * 解析AI操作指令
     */
    private fun parseAIOperation(content: String): AIOperationRequest? {
        val operationRegex = "\\[(\\w+):([^\\]]+)\\]".toRegex()
        val match = operationRegex.find(content) ?: return null

        val operationType = match.groupValues[1]
        val params = match.groupValues[2].split(":", limit = 3)

        return when (operationType) {
            "CREATE_FILE" -> {
                if (params.size >= 2) {
                    AIOperationRequest(
                        type = AIOperationType.CREATE_FILE,
                        path = params[0],
                        content = params.getOrNull(1) ?: ""
                    )
                } else null
            }
            "CREATE_FOLDER" -> {
                if (params.isNotEmpty()) {
                    AIOperationRequest(
                        type = AIOperationType.CREATE_FOLDER,
                        path = params[0]
                    )
                } else null
            }
            "EDIT_FILE" -> {
                if (params.size >= 2) {
                    AIOperationRequest(
                        type = AIOperationType.EDIT_FILE,
                        path = params[0],
                        content = params.getOrNull(1) ?: ""
                    )
                } else null
            }
            "DELETE_FILE" -> {
                if (params.isNotEmpty()) {
                    AIOperationRequest(
                        type = AIOperationType.DELETE_FILE,
                        path = params[0]
                    )
                } else null
            }
            "DELETE_FOLDER" -> {
                if (params.isNotEmpty()) {
                    AIOperationRequest(
                        type = AIOperationType.DELETE_FOLDER,
                        path = params[0]
                    )
                } else null
            }
            "WEB_SEARCH" -> {
                if (params.isNotEmpty()) {
                    AIOperationRequest(
                        type = AIOperationType.WEB_SEARCH,
                        path = "",
                        content = params[0]
                    )
                } else null
            }
            "READ_FILE" -> {
                if (params.isNotEmpty()) {
                    AIOperationRequest(
                        type = AIOperationType.READ_FILE,
                        path = params[0]
                    )
                } else null
            }
            "LIST_FILES" -> {
                AIOperationRequest(
                    type = AIOperationType.LIST_FILES,
                    path = params.getOrNull(0) ?: ""
                )
            }
            else -> null
        }
    }

    /**
     * 处理AI操作
     */
    private suspend fun handleAIOperation(operation: AIOperationRequest) {
        if (operation.type.requiresConfirmation) {
            // 需要用户确认的操作
            _pendingOperation.value = operation
        } else {
            // 直接执行的操作
            executeOperation(operation)
        }
    }

    /**
     * 执行AI操作
     */
    private suspend fun executeOperation(operation: AIOperationRequest) {
        val projectPath = _currentProjectPath.value
        if (projectPath == null) {
            addMessage(
                ChatMessage(
                    id = UUID.randomUUID().toString(),
                    content = "错误：未设置项目路径，无法执行文件操作",
                    isFromUser = false,
                    messageType = MessageType.ERROR
                )
            )
            return
        }

        try {
            val response = aiOperationService.executeOperation(operation, projectPath)
            _conversationHistory.value.addOperation(response)

            val resultMessage = ChatMessage(
                id = UUID.randomUUID().toString(),
                content = if (response.success) {
                    "✅ ${response.result}"
                } else {
                    "❌ 操作失败: ${response.error}"
                },
                isFromUser = false,
                messageType = if (response.success) MessageType.SYSTEM else MessageType.ERROR,
                operation = operation
            )
            addMessage(resultMessage)

        } catch (e: Exception) {
            addMessage(
                ChatMessage(
                    id = UUID.randomUUID().toString(),
                    content = "❌ 操作执行失败: ${e.message}",
                    isFromUser = false,
                    messageType = MessageType.ERROR
                )
            )
        }
    }

    /**
     * 确认待执行的操作
     */
    fun confirmPendingOperation() {
        viewModelScope.launch {
            val operation = _pendingOperation.value
            if (operation != null) {
                _pendingOperation.value = null
                executeOperation(operation)
            }
        }
    }

    /**
     * 取消待执行的操作
     */
    fun cancelPendingOperation() {
        _pendingOperation.value = null
        addMessage(
            ChatMessage(
                id = UUID.randomUUID().toString(),
                content = "操作已取消",
                isFromUser = false,
                messageType = MessageType.SYSTEM
            )
        )
    }

    /**
     * 清空聊天记录
     */
    fun clearChat() {
        _messages.value = emptyList()
        _conversationHistory.value = ConversationHistory(id = UUID.randomUUID().toString())

        // 重新添加欢迎消息
        addMessage(
            ChatMessage(
                id = UUID.randomUUID().toString(),
                content = "聊天记录已清空。有什么新的问题吗？",
                isFromUser = false,
                messageType = MessageType.SYSTEM
            )
        )
    }
    
    /**
     * 删除消息
     */
    fun deleteMessage(messageId: String) {
        _messages.value = _messages.value.filter { it.id != messageId }
    }
    
    /**
     * 复制消息内容
     */
    fun copyMessage(messageId: String): String? {
        return _messages.value.find { it.id == messageId }?.content
    }
    
    /**
     * 重新生成AI响应
     */
    fun regenerateResponse(userMessageId: String) {
        viewModelScope.launch {
            try {
                val userMessage = _messages.value.find { it.id == userMessageId }
                if (userMessage == null || userMessage.isFromUser.not()) return@launch
                
                // 删除之前的AI响应（如果存在）
                val messageIndex = _messages.value.indexOfFirst { it.id == userMessageId }
                if (messageIndex >= 0 && messageIndex < _messages.value.size - 1) {
                    val nextMessage = _messages.value[messageIndex + 1]
                    if (!nextMessage.isFromUser) {
                        deleteMessage(nextMessage.id)
                    }
                }
                
                // 重新生成响应
                _isTyping.value = true
                val aiResponse = generateAIResponse(userMessage.content)
                delay(1000 + (Math.random() * 2000).toLong())
                
                val aiMessage = ChatMessage(
                    id = UUID.randomUUID().toString(),
                    content = aiResponse.message,
                    isFromUser = false,
                    messageType = if (aiResponse.codeGenerated != null) MessageType.CODE else MessageType.TEXT,
                    codeSnippet = aiResponse.codeGenerated
                )
                addMessage(aiMessage)
                
            } catch (e: Exception) {
                _errorMessage.value = "重新生成响应失败: ${e.message}"
            } finally {
                _isTyping.value = false
            }
        }
    }
    
    /**
     * 添加消息到列表
     */
    private fun addMessage(message: ChatMessage) {
        _messages.value = _messages.value + message

        // 添加到对话历史
        _conversationHistory.value.addMessage(message)
    }
    
    /**
     * 简单AI响应数据类
     */
    private data class SimpleAIResponse(
        val message: String,
        val suggestions: List<String> = emptyList(),
        val codeGenerated: CodeSnippet? = null
    )

    /**
     * 生成AI响应（模拟）
     */
    private fun generateAIResponse(userInput: String): SimpleAIResponse {
        // 这里是模拟的AI响应逻辑，实际应用中应该调用真实的AI API
        
        val lowerInput = userInput.lowercase()
        
        return when {
            lowerInput.contains("hello") || lowerInput.contains("你好") -> {
                SimpleAIResponse(
                    message = "你好！很高兴为你提供编程帮助。请告诉我你需要什么帮助？",
                    suggestions = listOf("代码生成", "代码审查", "调试帮助", "技术问题")
                )
            }
            
            lowerInput.contains("kotlin") && (lowerInput.contains("class") || lowerInput.contains("类")) -> {
                SimpleAIResponse(
                    message = "我来为你生成一个Kotlin类的示例：",
                    codeGenerated = CodeSnippet(
                        code = """
                            class ExampleClass {
                                private var name: String = ""

                                constructor(name: String) {
                                    this.name = name
                                }

                                fun getName(): String {
                                    return name
                                }

                                fun setName(newName: String) {
                                    this.name = newName
                                }
                            }
                        """.trimIndent(),
                        language = "kotlin",
                        fileName = "ExampleClass.kt"
                    )
                )
            }
            
            lowerInput.contains("android") && lowerInput.contains("activity") -> {
                SimpleAIResponse(
                    message = "这是一个基本的Android Activity示例：",
                    codeGenerated = CodeSnippet(
                        code = """
                            class MainActivity : AppCompatActivity() {
                                override fun onCreate(savedInstanceState: Bundle?) {
                                    super.onCreate(savedInstanceState)
                                    setContentView(R.layout.activity_main)

                                    // 初始化视图和设置监听器
                                    initViews()
                                }

                                private fun initViews() {
                                    // 在这里初始化你的视图
                                }
                            }
                        """.trimIndent(),
                        language = "kotlin",
                        fileName = "MainActivity.kt"
                    )
                )
            }
            
            lowerInput.contains("help") || lowerInput.contains("帮助") -> {
                SimpleAIResponse(
                    message = "我可以帮助你：\n\n" +
                            "1. **代码生成** - 根据你的需求生成代码片段\n" +
                            "2. **代码审查** - 检查代码质量和提供改进建议\n" +
                            "3. **调试帮助** - 帮助解决代码中的问题\n" +
                            "4. **技术解释** - 解释编程概念和技术\n" +
                            "5. **最佳实践** - 提供编程最佳实践建议\n\n" +
                            "请具体描述你需要什么帮助！",
                    suggestions = listOf("生成Kotlin类", "Android开发问题", "代码优化", "设计模式")
                )
            }

            else -> {
                SimpleAIResponse(
                    message = "我理解你的问题。作为AI编程助手，我会尽力帮助你解决编程相关的问题。\n\n" +
                            "如果你有具体的代码问题或需要生成特定的代码，请提供更多详细信息，我会给出更准确的帮助。",
                    suggestions = listOf("提供更多细节", "展示代码示例", "说明具体需求")
                )
            }
        }
    }
    
    /**
     * 设置文件层次结构信息
     */
    fun setFileHierarchy(hierarchy: String) {
        _fileHierarchy.value = hierarchy

        // 添加系统消息通知AI文件结构已加载
        val hierarchyMessage = ChatMessage(
            id = UUID.randomUUID().toString(),
            content = "文件层次结构已加载完成。\n\n" +
                    "层次结构格式说明：\n" +
                    "- <A> 表示第一层文件夹A\n" +
                    "- <2\\a> 表示第二层文件夹a，数字2表示层级\n" +
                    "- <3?a.txt> 表示第三层文件a.txt，?表示这是文件\n" +
                    "- \\ 表示文件夹，? 表示文件\n" +
                    "- | 分隔不同的顶级文件夹\n\n" +
                    "当前项目包含 ${hierarchy.count { it == '?' }} 个文件，" +
                    "${hierarchy.count { it == '\\' }} 个文件夹。\n\n" +
                    "我现在可以帮助您：\n" +
                    "• 分析项目结构\n" +
                    "• 查找特定文件\n" +
                    "• 代码编辑和优化\n" +
                    "• 项目重构建议",
            isFromUser = false,
            messageType = MessageType.SYSTEM
        )

        addMessage(hierarchyMessage)
    }

    /**
     * 发送代码编辑相关的消息
     */
    fun sendCodeEditMessage(
        fileName: String,
        lineRange: String,
        codeContent: String,
        instruction: String
    ) {
        val message = buildString {
            appendLine("请帮我编辑以下代码：")
            appendLine()
            appendLine("文件：$fileName")
            appendLine("行号范围：$lineRange")
            appendLine()
            appendLine("指令：$instruction")
            appendLine()
            appendLine("代码：")
            appendLine("```")
            appendLine(codeContent)
            appendLine("```")
        }

        sendMessage(message)
    }

    /**
     * 生成增强的AI响应（包含文件层次结构上下文）
     */
    private fun generateEnhancedAIResponse(userInput: String): SimpleAIResponse {
        val lowerInput = userInput.lowercase()
        val hierarchy = _fileHierarchy.value

        return when {
            // 文件结构相关查询
            lowerInput.contains("文件结构") || lowerInput.contains("项目结构") -> {
                if (hierarchy != null) {
                    SimpleAIResponse(
                        message = "根据当前项目的文件层次结构，我来为您分析：\n\n" +
                                "项目包含：\n" +
                                "• ${hierarchy.count { it == '?' }} 个文件\n" +
                                "• ${hierarchy.count { it == '\\' }} 个文件夹\n" +
                                "• ${hierarchy.split("|").size} 个顶级目录\n\n" +
                                "主要目录结构：\n" +
                                parseHierarchyForDisplay(hierarchy) + "\n\n" +
                                "需要我帮您分析特定的文件或目录吗？",
                        suggestions = listOf("查看具体文件", "分析代码结构", "项目重构建议")
                    )
                } else {
                    SimpleAIResponse(
                        message = "项目文件结构还未加载完成，请稍等片刻。",
                        suggestions = listOf("等待加载完成", "手动刷新")
                    )
                }
            }

            // 查找文件
            lowerInput.contains("找") && (lowerInput.contains("文件") || lowerInput.contains(".")) -> {
                if (hierarchy != null) {
                    val searchResults = searchFilesInHierarchy(hierarchy, userInput)
                    SimpleAIResponse(
                        message = if (searchResults.isNotEmpty()) {
                            "找到以下相关文件：\n\n" + searchResults.joinToString("\n") { "• $it" }
                        } else {
                            "没有找到匹配的文件，请检查文件名是否正确。"
                        },
                        suggestions = listOf("查看文件内容", "编辑文件", "查找其他文件")
                    )
                } else {
                    SimpleAIResponse(
                        message = "文件结构未加载，无法搜索文件。",
                        suggestions = listOf("等待加载", "重新加载项目")
                    )
                }
            }

            // 代码编辑相关
            lowerInput.contains("编辑") || lowerInput.contains("修改") || lowerInput.contains("优化") -> {
                SimpleAIResponse(
                    message = "我可以帮您编辑和优化代码。请告诉我：\n\n" +
                            "1. 要编辑的文件名\n" +
                            "2. 具体的行号范围（支持16进制格式）\n" +
                            "3. 您希望进行什么样的修改\n\n" +
                            "例如：\"请优化MainActivity.kt的第10-20行的代码\"",
                    suggestions = listOf("选择代码范围", "描述修改需求", "查看编辑历史")
                )
            }

            else -> generateAIResponse(userInput)
        }
    }

    /**
     * 解析层次结构用于显示
     */
    private fun parseHierarchyForDisplay(hierarchy: String): String {
        val parts = hierarchy.split("|")
        val result = StringBuilder()

        parts.take(3).forEach { part -> // 只显示前3个顶级目录
            val regex = "<(\\d*)(\\\\|\\?)?([^<>]+)>".toRegex()
            val matches = regex.findAll(part)

            matches.take(5).forEach { match -> // 每个目录只显示前5个项目
                val level = if (match.groupValues[1].isEmpty()) 1 else match.groupValues[1].toInt()
                val type = match.groupValues[2]
                val name = match.groupValues[3]

                val indent = "  ".repeat(level - 1)
                val icon = when (type) {
                    "?" -> "📄"
                    "\\" -> "📁"
                    else -> "📁"
                }

                result.appendLine("$indent$icon $name")
            }
        }

        if (parts.size > 3) {
            result.appendLine("... 还有 ${parts.size - 3} 个目录")
        }

        return result.toString()
    }

    /**
     * 在层次结构中搜索文件
     */
    private fun searchFilesInHierarchy(hierarchy: String, query: String): List<String> {
        val results = mutableListOf<String>()
        val regex = "<(\\d*)(\\\\|\\?)?([^<>]+)>".toRegex()
        val matches = regex.findAll(hierarchy)

        matches.forEach { match ->
            val type = match.groupValues[2]
            val name = match.groupValues[3]

            if (type == "?" && name.contains(query.replace("找", "").trim(), ignoreCase = true)) {
                results.add(name)
            }
        }

        return results.take(10) // 最多返回10个结果
    }

    /**
     * 清除错误消息
     */
    fun clearError() {
        _errorMessage.value = null
    }
}

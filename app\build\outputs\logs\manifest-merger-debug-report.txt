-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:2:1-38:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:2:1-38:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:2:1-38:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:2:1-38:12
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\480e48a0c67746a2119bae90759d0e56\transformed\navigation-common-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1bcc5f54811ec933f8da6d9573fed16\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae68ee2c5ad45df76c15f4f92f82c5e7\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b71c0080d09b7e953137848cfdb971af\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8dc0d352d345aa35f2ee10e52944ab8\transformed\navigation-compose-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\095e905be20fb6759056e386840ea417\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\661e1873c480683e880cc1ce1ce88984\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\11d7425afce467e09d70c569cfae21df\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c44abf07ab5a394388790862f3615e95\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\baca26586c094906a339eeeadb110e28\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a9eb4a1e8c3752182f0d47366147a11\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\120fccff2f0a1dc51fd38fc893fa2c51\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8106db5b48b9b44ee005fcb2ad5eb9ef\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf230a6f3b2ad85d9953707bebc1bd\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90d0b806c934f6b87331a3e38823f218\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f970e25853225cdced145e4acf53caa7\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7573d25c53722a3a9cea6b2d4fc3106c\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1edce45756448d9e43942e4d82a87725\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc8fb44121baf7435b31c9d6269064ba\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a55c6f6162a830b4dc56f89efebee8\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e75c2872e375a42dad9da04ff9c538ad\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70645bf502393932454a9dd718537c81\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f451975a29f2bcf44cf51ad5e24c36d\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61bba18f58263f18fdd415251a46f035\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\168afa63dcf159c0364da0cc1dcd871d\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f12c352bcb7d7783c797d1e336d27eb\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\114f08b47ba13e42f2b28322800d9308\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afacc2dd52ab1000bbce21dc75a74c42\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5e7fe94e2f8cef110eb603bad11e192\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb90fc506c1ed39be48b779709922568\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc18e2f3d57f534d5604452c29390dba\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c7cc1d9640a1b763cb5574c9351c79c\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62b76a3e3776ed85622743253098b7f2\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2dea0552a1165b4526d434f4a4c160c0\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9255637b8829de4ab4fc2e4c44d434fa\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b34790c16c0360667efebd6766ad7cd\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3b35bdc2bee2700fd3483842f13a4e4\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61d702f9086eebe3bb4fe8c633b5114e\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa51a1ddc9f74fbf4af54e56227f4d7a\transformed\ui-test-manifest-1.6.7\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d1a16a68aac2073f5f7e74321cee595\transformed\activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8326376e07dbfca2ef5f2a95089e9e0\transformed\activity-ktx-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a773f4123657a1714ef4c781606114d3\transformed\activity-compose-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7b838de34addc184d365d2dae0c68d3\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61e9be1bd5d7ef4582b201cc52f5686d\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b2987c92226881ac4b6ad237d7c45a58\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08f76b95ea81b588ae9e01a3accbaa57\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d02a7299267ac64286ea3dc4c5bd379b\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d06b774178c0934a97a4383b74c0a17\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ca66080cfb92d704ba6df0e0d2a172d\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e93f278eccfb7a4e62fd0a4bc0634db3\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:6:5-80
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:6:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:7:5-81
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:7:22-78
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:8:5-9:40
	tools:ignore
		ADDED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:9:9-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:8:22-79
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:12:5-67
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:12:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:13:5-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:13:22-76
application
ADDED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:15:5-36:19
INJECTED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:15:5-36:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70645bf502393932454a9dd718537c81\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70645bf502393932454a9dd718537c81\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f451975a29f2bcf44cf51ad5e24c36d\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f451975a29f2bcf44cf51ad5e24c36d\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61bba18f58263f18fdd415251a46f035\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61bba18f58263f18fdd415251a46f035\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9255637b8829de4ab4fc2e4c44d434fa\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9255637b8829de4ab4fc2e4c44d434fa\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa51a1ddc9f74fbf4af54e56227f4d7a\transformed\ui-test-manifest-1.6.7\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa51a1ddc9f74fbf4af54e56227f4d7a\transformed\ui-test-manifest-1.6.7\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08f76b95ea81b588ae9e01a3accbaa57\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08f76b95ea81b588ae9e01a3accbaa57\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d06b774178c0934a97a4383b74c0a17\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d06b774178c0934a97a4383b74c0a17\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9255637b8829de4ab4fc2e4c44d434fa\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:22:9-35
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:20:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:18:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:21:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:24:9-29
	android:icon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:19:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:16:9-35
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:23:9-42
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:17:9-65
activity#ai.aiot.MainActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:25:9-35:20
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:28:13-45
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:27:13-36
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:29:13-46
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:26:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:30:13-34:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:31:17-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:31:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:33:17-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml:33:27-74
uses-sdk
INJECTED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\480e48a0c67746a2119bae90759d0e56\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\480e48a0c67746a2119bae90759d0e56\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1bcc5f54811ec933f8da6d9573fed16\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1bcc5f54811ec933f8da6d9573fed16\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae68ee2c5ad45df76c15f4f92f82c5e7\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae68ee2c5ad45df76c15f4f92f82c5e7\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b71c0080d09b7e953137848cfdb971af\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b71c0080d09b7e953137848cfdb971af\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8dc0d352d345aa35f2ee10e52944ab8\transformed\navigation-compose-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8dc0d352d345aa35f2ee10e52944ab8\transformed\navigation-compose-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\095e905be20fb6759056e386840ea417\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\095e905be20fb6759056e386840ea417\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\661e1873c480683e880cc1ce1ce88984\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\661e1873c480683e880cc1ce1ce88984\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\11d7425afce467e09d70c569cfae21df\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\11d7425afce467e09d70c569cfae21df\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c44abf07ab5a394388790862f3615e95\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c44abf07ab5a394388790862f3615e95\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\baca26586c094906a339eeeadb110e28\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\baca26586c094906a339eeeadb110e28\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a9eb4a1e8c3752182f0d47366147a11\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a9eb4a1e8c3752182f0d47366147a11\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\120fccff2f0a1dc51fd38fc893fa2c51\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\120fccff2f0a1dc51fd38fc893fa2c51\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8106db5b48b9b44ee005fcb2ad5eb9ef\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8106db5b48b9b44ee005fcb2ad5eb9ef\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf230a6f3b2ad85d9953707bebc1bd\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf230a6f3b2ad85d9953707bebc1bd\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90d0b806c934f6b87331a3e38823f218\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90d0b806c934f6b87331a3e38823f218\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f970e25853225cdced145e4acf53caa7\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f970e25853225cdced145e4acf53caa7\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7573d25c53722a3a9cea6b2d4fc3106c\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7573d25c53722a3a9cea6b2d4fc3106c\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1edce45756448d9e43942e4d82a87725\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1edce45756448d9e43942e4d82a87725\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc8fb44121baf7435b31c9d6269064ba\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc8fb44121baf7435b31c9d6269064ba\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a55c6f6162a830b4dc56f89efebee8\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a55c6f6162a830b4dc56f89efebee8\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e75c2872e375a42dad9da04ff9c538ad\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e75c2872e375a42dad9da04ff9c538ad\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70645bf502393932454a9dd718537c81\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70645bf502393932454a9dd718537c81\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f451975a29f2bcf44cf51ad5e24c36d\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f451975a29f2bcf44cf51ad5e24c36d\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61bba18f58263f18fdd415251a46f035\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61bba18f58263f18fdd415251a46f035\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\168afa63dcf159c0364da0cc1dcd871d\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\168afa63dcf159c0364da0cc1dcd871d\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f12c352bcb7d7783c797d1e336d27eb\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f12c352bcb7d7783c797d1e336d27eb\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\114f08b47ba13e42f2b28322800d9308\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\114f08b47ba13e42f2b28322800d9308\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afacc2dd52ab1000bbce21dc75a74c42\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afacc2dd52ab1000bbce21dc75a74c42\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5e7fe94e2f8cef110eb603bad11e192\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5e7fe94e2f8cef110eb603bad11e192\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb90fc506c1ed39be48b779709922568\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb90fc506c1ed39be48b779709922568\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc18e2f3d57f534d5604452c29390dba\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc18e2f3d57f534d5604452c29390dba\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c7cc1d9640a1b763cb5574c9351c79c\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c7cc1d9640a1b763cb5574c9351c79c\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62b76a3e3776ed85622743253098b7f2\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62b76a3e3776ed85622743253098b7f2\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2dea0552a1165b4526d434f4a4c160c0\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2dea0552a1165b4526d434f4a4c160c0\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9255637b8829de4ab4fc2e4c44d434fa\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9255637b8829de4ab4fc2e4c44d434fa\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b34790c16c0360667efebd6766ad7cd\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b34790c16c0360667efebd6766ad7cd\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3b35bdc2bee2700fd3483842f13a4e4\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3b35bdc2bee2700fd3483842f13a4e4\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61d702f9086eebe3bb4fe8c633b5114e\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61d702f9086eebe3bb4fe8c633b5114e\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa51a1ddc9f74fbf4af54e56227f4d7a\transformed\ui-test-manifest-1.6.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa51a1ddc9f74fbf4af54e56227f4d7a\transformed\ui-test-manifest-1.6.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d1a16a68aac2073f5f7e74321cee595\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d1a16a68aac2073f5f7e74321cee595\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8326376e07dbfca2ef5f2a95089e9e0\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8326376e07dbfca2ef5f2a95089e9e0\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a773f4123657a1714ef4c781606114d3\transformed\activity-compose-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a773f4123657a1714ef4c781606114d3\transformed\activity-compose-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7b838de34addc184d365d2dae0c68d3\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7b838de34addc184d365d2dae0c68d3\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61e9be1bd5d7ef4582b201cc52f5686d\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61e9be1bd5d7ef4582b201cc52f5686d\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b2987c92226881ac4b6ad237d7c45a58\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b2987c92226881ac4b6ad237d7c45a58\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08f76b95ea81b588ae9e01a3accbaa57\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08f76b95ea81b588ae9e01a3accbaa57\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d02a7299267ac64286ea3dc4c5bd379b\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d02a7299267ac64286ea3dc4c5bd379b\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d06b774178c0934a97a4383b74c0a17\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d06b774178c0934a97a4383b74c0a17\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ca66080cfb92d704ba6df0e0d2a172d\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ca66080cfb92d704ba6df0e0d2a172d\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e93f278eccfb7a4e62fd0a4bc0634db3\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e93f278eccfb7a4e62fd0a4bc0634db3\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\aiout\app\src\main\AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70645bf502393932454a9dd718537c81\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70645bf502393932454a9dd718537c81\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70645bf502393932454a9dd718537c81\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f451975a29f2bcf44cf51ad5e24c36d\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61bba18f58263f18fdd415251a46f035\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61bba18f58263f18fdd415251a46f035\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d06b774178c0934a97a4383b74c0a17\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d06b774178c0934a97a4383b74c0a17\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f451975a29f2bcf44cf51ad5e24c36d\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f451975a29f2bcf44cf51ad5e24c36d\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f451975a29f2bcf44cf51ad5e24c36d\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f451975a29f2bcf44cf51ad5e24c36d\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f451975a29f2bcf44cf51ad5e24c36d\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f451975a29f2bcf44cf51ad5e24c36d\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f451975a29f2bcf44cf51ad5e24c36d\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61bba18f58263f18fdd415251a46f035\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61bba18f58263f18fdd415251a46f035\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61bba18f58263f18fdd415251a46f035\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9255637b8829de4ab4fc2e4c44d434fa\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9255637b8829de4ab4fc2e4c44d434fa\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9255637b8829de4ab4fc2e4c44d434fa\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#ai.aiot.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9255637b8829de4ab4fc2e4c44d434fa\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9255637b8829de4ab4fc2e4c44d434fa\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9255637b8829de4ab4fc2e4c44d434fa\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9255637b8829de4ab4fc2e4c44d434fa\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9255637b8829de4ab4fc2e4c44d434fa\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#ai.aiot.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9255637b8829de4ab4fc2e4c44d434fa\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9255637b8829de4ab4fc2e4c44d434fa\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa51a1ddc9f74fbf4af54e56227f4d7a\transformed\ui-test-manifest-1.6.7\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa51a1ddc9f74fbf4af54e56227f4d7a\transformed\ui-test-manifest-1.6.7\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa51a1ddc9f74fbf4af54e56227f4d7a\transformed\ui-test-manifest-1.6.7\AndroidManifest.xml:24:13-63
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\df85c8f90fe5ebc963315c8cac9027eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92

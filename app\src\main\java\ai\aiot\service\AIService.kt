package ai.aiot.service

import ai.aiot.model.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.json.Json
import java.io.IOException
import java.net.HttpURLConnection
import java.net.URL

/**
 * AI服务管理器
 */
class AIService {
    
    private val json = Json {
        ignoreUnknownKeys = true
        prettyPrint = true
    }
    
    /**
     * 发送AI请求
     */
    suspend fun sendRequest(request: AIRequest, config: AIConfig): AIResponse {
        return withContext(Dispatchers.IO) {
            when (config.provider) {
                AIProvider.OPENAI -> sendOpenAIRequest(request, config)
                AIProvider.CLAUDE -> sendClaudeRequest(request, config)
                AIProvider.GEMINI -> sendGeminiRequest(request, config)
                AIProvider.DEEPSEEK -> sendDeepSeekRequest(request, config)
                AIProvider.CUSTOM -> sendCustomRequest(request, config)
            }
        }
    }

    /**
     * 发送聊天请求（兼容旧版本）
     */
    suspend fun sendChatRequest(messages: List<AIMessage>, config: AIConfig): Result<AIResponse> {
        return withContext(Dispatchers.IO) {
            try {
                val request = AIRequest(
                    messages = messages,
                    model = config.model,
                    maxTokens = config.maxTokens,
                    temperature = config.temperature
                )

                val response = sendRequest(request, config)
                Result.success(response)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }
    
    /**
     * 发送代码编辑请求
     */
    suspend fun sendCodeEditRequest(editRequest: CodeEditRequest): Result<CodeEditResponse> {
        return withContext(Dispatchers.IO) {
            try {
                val prompt = editRequest.generatePrompt()
                val messages = listOf(
                    AIMessage("system", "你是一个专业的代码编辑助手，专门帮助用户修改和优化代码。"),
                    AIMessage("user", prompt)
                )
                
                val aiResponse = sendChatRequest(messages).getOrThrow()
                val responseContent = aiResponse.getContent()
                
                val modifiedCode = extractCodeBlock(responseContent, editRequest.language)
                val explanation = extractExplanation(responseContent)
                
                val codeEditResponse = CodeEditResponse(
                    originalCode = editRequest.content,
                    modifiedCode = modifiedCode,
                    explanation = explanation,
                    lineRange = editRequest.lineRange
                )
                
                Result.success(codeEditResponse)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }
    
    /**
     * 发送OpenAI请求
     */
    private suspend fun sendOpenAIRequest(request: AIRequest, config: AIConfig): AIResponse {
        val url = URL(config.apiUrl)
        val connection = url.openConnection() as HttpURLConnection
        
        try {
            connection.requestMethod = "POST"
            connection.setRequestProperty("Content-Type", "application/json")
            connection.setRequestProperty("Authorization", "Bearer ${config.apiKey}")
            
            // 添加自定义头部
            config.customHeaders.forEach { (key, value) ->
                connection.setRequestProperty(key, value)
            }
            
            connection.doOutput = true
            connection.connectTimeout = config.timeout.toInt()
            connection.readTimeout = config.timeout.toInt()
            
            // 发送请求
            val requestJson = json.encodeToString(request)
            connection.outputStream.use { it.write(requestJson.toByteArray()) }
            
            // 读取响应
            val responseCode = connection.responseCode
            val responseText = if (responseCode == HttpURLConnection.HTTP_OK) {
                connection.inputStream.bufferedReader().use { it.readText() }
            } else {
                connection.errorStream?.bufferedReader()?.use { it.readText() } ?: "Unknown error"
            }
            
            if (responseCode != HttpURLConnection.HTTP_OK) {
                throw IOException("HTTP $responseCode: $responseText")
            }
            
            return json.decodeFromString<AIResponse>(responseText)
            
        } finally {
            connection.disconnect()
        }
    }
    
    /**
     * 发送Claude请求
     */
    private suspend fun sendClaudeRequest(request: AIRequest, config: AIConfig): AIResponse {
        val url = URL(config.apiUrl)
        val connection = url.openConnection() as HttpURLConnection
        
        try {
            connection.requestMethod = "POST"
            connection.setRequestProperty("Content-Type", "application/json")
            connection.setRequestProperty("x-api-key", config.apiKey)
            connection.setRequestProperty("anthropic-version", "2023-06-01")
            
            connection.doOutput = true
            connection.connectTimeout = config.timeout.toInt()
            connection.readTimeout = config.timeout.toInt()
            
            // Claude API格式转换
            val claudeRequest = mapOf(
                "model" to request.model,
                "max_tokens" to request.maxTokens,
                "temperature" to request.temperature,
                "messages" to request.messages.map { message ->
                    mapOf(
                        "role" to message.role,
                        "content" to message.content
                    )
                }
            )
            
            val requestJson = json.encodeToString(claudeRequest)
            connection.outputStream.use { it.write(requestJson.toByteArray()) }
            
            val responseCode = connection.responseCode
            val responseText = if (responseCode == HttpURLConnection.HTTP_OK) {
                connection.inputStream.bufferedReader().use { it.readText() }
            } else {
                connection.errorStream?.bufferedReader()?.use { it.readText() } ?: "Unknown error"
            }
            
            if (responseCode != HttpURLConnection.HTTP_OK) {
                throw IOException("HTTP $responseCode: $responseText")
            }
            
            // 转换Claude响应格式为标准格式
            return convertClaudeResponse(responseText)
            
        } finally {
            connection.disconnect()
        }
    }
    
    /**
     * 发送Gemini请求
     */
    private suspend fun sendGeminiRequest(request: AIRequest, config: AIConfig): AIResponse {
        val url = URL("${config.apiUrl}/${request.model}:generateContent?key=${config.apiKey}")
        val connection = url.openConnection() as HttpURLConnection
        
        try {
            connection.requestMethod = "POST"
            connection.setRequestProperty("Content-Type", "application/json")
            
            connection.doOutput = true
            connection.connectTimeout = config.timeout.toInt()
            connection.readTimeout = config.timeout.toInt()
            
            // Gemini API格式转换
            val geminiRequest = mapOf(
                "contents" to request.messages.map { message ->
                    mapOf(
                        "parts" to listOf(mapOf("text" to message.content)),
                        "role" to if (message.role == "assistant") "model" else "user"
                    )
                },
                "generationConfig" to mapOf(
                    "temperature" to request.temperature,
                    "maxOutputTokens" to request.maxTokens
                )
            )
            
            val requestJson = json.encodeToString(geminiRequest)
            connection.outputStream.use { it.write(requestJson.toByteArray()) }
            
            val responseCode = connection.responseCode
            val responseText = if (responseCode == HttpURLConnection.HTTP_OK) {
                connection.inputStream.bufferedReader().use { it.readText() }
            } else {
                connection.errorStream?.bufferedReader()?.use { it.readText() } ?: "Unknown error"
            }
            
            if (responseCode != HttpURLConnection.HTTP_OK) {
                throw IOException("HTTP $responseCode: $responseText")
            }
            
            return convertGeminiResponse(responseText)
            
        } finally {
            connection.disconnect()
        }
    }
    
    /**
     * 发送自定义请求
     */
    private suspend fun sendCustomRequest(request: AIRequest, config: AIConfig): AIResponse {
        // 对于自定义API，使用标准格式
        return sendOpenAIRequest(request, config)
    }

    /**
     * 发送DeepSeek请求
     */
    private suspend fun sendDeepSeekRequest(request: AIRequest, config: AIConfig): AIResponse {
        val url = URL(config.apiUrl)
        val connection = url.openConnection() as HttpURLConnection

        try {
            connection.requestMethod = "POST"
            connection.setRequestProperty("Content-Type", "application/json")
            connection.setRequestProperty("Authorization", "Bearer ${config.apiKey}")

            // 添加自定义头部
            config.customHeaders.forEach { (key, value) ->
                connection.setRequestProperty(key, value)
            }

            connection.doOutput = true
            connection.connectTimeout = config.timeout.toInt()
            connection.readTimeout = config.timeout.toInt()

            // 发送请求
            val requestJson = json.encodeToString(request)
            connection.outputStream.use { it.write(requestJson.toByteArray()) }

            // 读取响应
            val responseCode = connection.responseCode
            val responseText = if (responseCode == HttpURLConnection.HTTP_OK) {
                connection.inputStream.bufferedReader().use { it.readText() }
            } else {
                connection.errorStream?.bufferedReader()?.use { it.readText() } ?: "Unknown error"
            }

            if (responseCode != HttpURLConnection.HTTP_OK) {
                throw IOException("HTTP $responseCode: $responseText")
            }

            return json.decodeFromString<AIResponse>(responseText)

        } finally {
            connection.disconnect()
        }
    }
    
    /**
     * 转换Claude响应格式
     */
    private fun convertClaudeResponse(responseText: String): AIResponse {
        // 这里需要根据Claude API的实际响应格式进行转换
        // 简化处理，实际使用时需要完整实现
        return try {
            val response = json.decodeFromString<Map<String, Any>>(responseText)
            val content = response["content"] as? List<*>
            val text = (content?.firstOrNull() as? Map<*, *>)?.get("text") as? String ?: ""
            
            AIResponse(
                choices = listOf(
                    AIChoice(
                        message = AIMessage("assistant", text)
                    )
                )
            )
        } catch (e: Exception) {
            AIResponse(error = AIError("Failed to parse Claude response: ${e.message}"))
        }
    }
    
    /**
     * 转换Gemini响应格式
     */
    private fun convertGeminiResponse(responseText: String): AIResponse {
        return try {
            val response = json.decodeFromString<Map<String, Any>>(responseText)
            val candidates = response["candidates"] as? List<*>
            val content = (candidates?.firstOrNull() as? Map<*, *>)?.get("content") as? Map<*, *>
            val parts = content?.get("parts") as? List<*>
            val text = (parts?.firstOrNull() as? Map<*, *>)?.get("text") as? String ?: ""
            
            AIResponse(
                choices = listOf(
                    AIChoice(
                        message = AIMessage("assistant", text)
                    )
                )
            )
        } catch (e: Exception) {
            AIResponse(error = AIError("Failed to parse Gemini response: ${e.message}"))
        }
    }
    
    /**
     * 提取代码块
     */
    private fun extractCodeBlock(response: String, language: String): String {
        val codeBlockRegex = "```$language\\s*\\n([\\s\\S]*?)\\n```".toRegex()
        val match = codeBlockRegex.find(response)
        return match?.groupValues?.get(1)?.trim() ?: response
    }
    
    /**
     * 提取说明文本
     */
    private fun extractExplanation(response: String): String {
        val explanationRegex = "修改说明：\\s*\\n([\\s\\S]*)".toRegex()
        val match = explanationRegex.find(response)
        return match?.groupValues?.get(1)?.trim() ?: ""
    }
    
    /**
     * 测试连接
     */
    suspend fun testConnection(): Result<String> {
        return withContext(Dispatchers.IO) {
            try {
                val testMessages = listOf(
                    AIMessage("user", "Hello, please respond with 'Connection successful'")
                )
                
                val response = sendChatRequest(testMessages).getOrThrow()
                if (response.isSuccess()) {
                    Result.success("连接成功")
                } else {
                    Result.failure(Exception(response.error?.message ?: "Unknown error"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }
}

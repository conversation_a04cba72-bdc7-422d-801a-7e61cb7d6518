# 🚀 AI编程IDE 完整使用指南

## 📋 概述

这是一个功能完整的AI编程IDE Android应用，集成了先进的文件管理、智能代码编辑和AI助手功能。应用采用您指定的文件层次结构存储格式，支持16进制行号系统，并提供完整的AI编辑权限管理。

## 🎯 核心功能

### 1. 项目管理系统
- **多项目支持**：可以创建和管理多个编程项目
- **文件夹导入**：支持导入多个文件夹到项目中
- **权限控制**：精确控制AI对不同文件夹的编辑权限
- **项目统计**：实时显示项目文件数量和结构信息

### 2. 文件层次结构存储
- **格式规范**：`<A><2\a><3\a.txt>|<B>` 
  - `<A>` = 第一层文件夹A（1省略）
  - `<2\a>` = 第二层文件夹a，数字表示层级
  - `<3?a.txt>` = 第三层文件a.txt，?表示文件
  - `\` = 文件夹标识，`?` = 文件标识
  - `|` = 分隔不同顶级文件夹
- **AI可读**：层次结构存储为隐藏文件，AI具有读取权限
- **实时更新**：文件结构变化时自动更新层次信息

### 3. 智能代码编辑系统
- **16进制行号**：使用16进制表示行号（如 `0x3F` 代替 63）
- **精确定位**：支持精确的代码范围选择和修改
- **AI编辑建议**：AI可以提出代码修改建议
- **修改申请机制**：AI发现明显错误时向用户申请修改权限
- **撤销/重做**：完整的编辑历史管理

### 4. AI配置管理
- **多服务商支持**：
  - OpenAI (GPT-3.5, GPT-4)
  - Claude (Anthropic)
  - Gemini (Google)
  - 自定义API
- **灵活配置**：API密钥、URL、模型参数完全可定制
- **连接测试**：内置API连接测试功能

### 5. 文件分析和加载
- **智能分析**：自动分析项目文件结构
- **加载进度**：实时显示文件扫描和分析进度
- **结构预览**：可视化展示项目层次结构
- **AI通知**：分析完成后自动通知AI助手

## 🔧 使用流程

### 第一步：启动应用
1. 首次启动会请求文件访问权限
2. 授予权限后进入项目选择界面

### 第二步：创建项目
1. 点击右下角的"+"按钮
2. 填写项目信息：
   - 项目名称
   - 项目描述（可选）
   - 根路径
3. 添加要包含的文件夹：
   - 选择文件夹路径
   - 设置显示名称
   - 配置AI编辑权限
   - 选择是否包含子文件夹

### 第三步：配置AI服务
1. 点击左侧导航栏的"AI配置"
2. 选择AI服务提供商
3. 输入API密钥和配置参数
4. 测试连接确保配置正确
5. 保存配置

### 第四步：开始编程
1. **文件管理**：左侧面板浏览项目文件
2. **代码编辑**：点击代码文件开始编辑
3. **AI助手**：切换到AI面板获取帮助

## 🤖 AI功能详解

### 文件结构理解
AI助手会自动获得项目的完整文件层次结构，可以：
- 分析项目架构
- 查找特定文件
- 理解代码组织结构
- 提供重构建议

### 智能代码编辑
1. **选择代码范围**：在编辑器中选择要修改的代码
2. **发送编辑请求**：描述您希望的修改
3. **AI分析处理**：AI分析代码并生成修改建议
4. **预览和应用**：查看修改建议，选择应用或拒绝

### 16进制行号系统
- **精确定位**：使用16进制行号实现精确的代码定位
- **Token效率**：16进制表示占用更少的AI Token
- **范围表示**：如 `[0x64-0xC8]` 表示第100-200行

### AI编辑权限
- **文件级权限**：可以为每个文件夹设置AI编辑权限
- **扩展名过滤**：支持特定文件类型的编辑限制
- **大小限制**：超过指定大小的文件需要确认
- **修改申请**：AI发现问题时会主动申请修改权限

## 📱 界面导航

### 左侧导航栏
- **文件**：文件管理器和项目浏览
- **编辑器**：代码编辑和查看
- **AI助手**：智能对话和文件分析
- **AI配置**：AI服务设置和测试
- **设置**：应用设置和偏好

### 主要面板
- **文件管理面板**：树形结构显示项目文件
- **代码编辑面板**：支持语法高亮的代码编辑器
- **AI聊天面板**：与AI助手的对话界面
- **文件分析面板**：显示项目结构分析结果
- **编辑建议面板**：显示AI的代码修改建议

## 🔒 安全和权限

### 文件访问权限
- **存储权限**：需要读写外部存储权限
- **应用专用目录**：优先使用应用专用目录
- **权限管理**：支持细粒度的文件访问控制

### AI编辑安全
- **权限验证**：每次AI编辑都需要权限验证
- **用户确认**：重要修改需要用户明确确认
- **历史记录**：完整记录所有AI编辑操作
- **回滚机制**：支持撤销AI的修改

## 🚨 故障排除

### 常见问题
1. **权限问题**：确保授予了存储访问权限
2. **AI连接失败**：检查网络连接和API配置
3. **文件加载慢**：大型项目需要更多时间分析
4. **编辑建议不准确**：可以调整AI模型参数

### 性能优化
- **文件过滤**：使用排除模式过滤不需要的文件
- **层次深度限制**：避免过深的文件夹嵌套
- **定期清理**：清理不需要的项目和缓存

## 🔮 高级功能

### 自定义AI提示
- 可以自定义AI的系统提示词
- 支持特定编程语言的优化提示
- 可以设置代码风格偏好

### 批量操作
- 支持批量文件重命名
- 批量代码格式化
- 批量AI优化建议

### 扩展性
- 插件系统预留接口
- 支持自定义文件类型
- 可扩展的AI服务接口

---

## 🎉 开始使用

现在您已经了解了AI编程IDE的完整功能，可以开始创建您的第一个项目了！

1. 启动应用并授予权限
2. 创建新项目并添加文件夹
3. 配置您的AI服务
4. 开始智能编程之旅！

**享受AI增强的编程体验！** 🚀

package ai.aiot.model

/**
 * 编辑器状态数据模型
 */
data class EditorState(
    val content: String = "",
    val cursorPosition: Int = 0,
    val selectionStart: Int = 0,
    val selectionEnd: Int = 0,
    val fileName: String = "",
    val filePath: String = "",
    val language: String = "",
    val isModified: Boolean = false,
    val lineNumbers: List<Int> = emptyList(),
    val currentLine: Int = 1,
    val currentColumn: Int = 1
) {
    /**
     * 获取选中的文本
     */
    fun getSelectedText(): String {
        return if (selectionStart != selectionEnd) {
            content.substring(selectionStart, selectionEnd)
        } else ""
    }
    
    /**
     * 获取当前行的文本
     */
    fun getCurrentLineText(): String {
        val lines = content.split('\n')
        return if (currentLine <= lines.size) {
            lines[currentLine - 1]
        } else ""
    }
    
    /**
     * 计算行号和列号
     */
    fun calculateLineAndColumn(position: Int): Pair<Int, Int> {
        val beforeCursor = content.substring(0, position.coerceAtMost(content.length))
        val lines = beforeCursor.split('\n')
        val line = lines.size
        val column = lines.lastOrNull()?.length ?: 0
        return Pair(line, column + 1)
    }
}

/**
 * 编辑器操作类型
 */
enum class EditorAction {
    INSERT,
    DELETE,
    REPLACE,
    UNDO,
    REDO,
    SAVE,
    FORMAT
}

/**
 * 编辑器操作记录
 */
data class EditorOperation(
    val action: EditorAction,
    val position: Int,
    val oldText: String,
    val newText: String,
    val timestamp: Long = System.currentTimeMillis()
)

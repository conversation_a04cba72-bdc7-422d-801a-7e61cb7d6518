package ai.aiot.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.unit.dp

/**
 * 创建文件/文件夹对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CreateItemDialog(
    type: CreateType,
    onConfirm: (String) -> Unit,
    onDismiss: () -> Unit
) {
    var name by remember { mutableStateOf("") }
    var isError by remember { mutableStateOf(false) }
    val focusRequester = remember { FocusRequester() }
    val keyboardController = LocalSoftwareKeyboardController.current
    
    val title = when (type) {
        CreateType.FILE -> "新建文件"
        CreateType.FOLDER -> "新建文件夹"
    }
    
    val placeholder = when (type) {
        CreateType.FILE -> "输入文件名（如：MainActivity.kt）"
        CreateType.FOLDER -> "输入文件夹名"
    }
    
    LaunchedEffect(Unit) {
        focusRequester.requestFocus()
    }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(text = title)
        },
        text = {
            Column {
                OutlinedTextField(
                    value = name,
                    onValueChange = { 
                        name = it
                        isError = false
                    },
                    label = { Text("名称") },
                    placeholder = { Text(placeholder) },
                    isError = isError,
                    supportingText = if (isError) {
                        { Text("请输入有效的名称") }
                    } else null,
                    keyboardOptions = KeyboardOptions(
                        imeAction = ImeAction.Done
                    ),
                    keyboardActions = KeyboardActions(
                        onDone = {
                            keyboardController?.hide()
                            if (validateName(name, type)) {
                                onConfirm(name.trim())
                            } else {
                                isError = true
                            }
                        }
                    ),
                    modifier = Modifier
                        .fillMaxWidth()
                        .focusRequester(focusRequester),
                    singleLine = true
                )
                
                if (type == CreateType.FILE) {
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "提示：请包含文件扩展名（如：.kt, .java, .xml）",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    if (validateName(name, type)) {
                        onConfirm(name.trim())
                    } else {
                        isError = true
                    }
                }
            ) {
                Text("创建")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

/**
 * 验证名称是否有效
 */
private fun validateName(name: String, type: CreateType): Boolean {
    val trimmedName = name.trim()
    
    if (trimmedName.isEmpty()) {
        return false
    }
    
    // 检查非法字符
    val invalidChars = setOf('/', '\\', ':', '*', '?', '"', '<', '>', '|')
    if (trimmedName.any { it in invalidChars }) {
        return false
    }
    
    // 对于文件，建议包含扩展名
    if (type == CreateType.FILE && !trimmedName.contains('.')) {
        // 可以允许没有扩展名的文件，但给出提示
        return true
    }
    
    return true
}

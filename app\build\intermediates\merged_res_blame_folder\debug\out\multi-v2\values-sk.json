{"logs": [{"outputFile": "ai.aiot.app-mergeDebugResources-49:/values-sk/values-sk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9255637b8829de4ab4fc2e4c44d434fa\\transformed\\core-1.12.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,792", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,888"}, "to": {"startLines": "2,3,4,5,6,7,8,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,201,303,404,502,612,720,8114", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "196,298,399,497,607,715,837,8210"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\61d702f9086eebe3bb4fe8c633b5114e\\transformed\\ui-release\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,284,379,482,574,653,747,837,918,987,1056,1139,1226,1298,1376,1444", "endColumns": "94,83,94,102,91,78,93,89,80,68,68,82,86,71,77,67,113", "endOffsets": "195,279,374,477,569,648,742,832,913,982,1051,1134,1221,1293,1371,1439,1553"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "842,937,1021,1116,1219,1311,1390,7563,7653,7734,7803,7872,7955,8042,8215,8293,8361", "endColumns": "94,83,94,102,91,78,93,89,80,68,68,82,86,71,77,67,113", "endOffsets": "932,1016,1111,1214,1306,1385,1479,7648,7729,7798,7867,7950,8037,8109,8288,8356,8470"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\095e905be20fb6759056e386840ea417\\transformed\\material3-release\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,285,395,510,608,703,815,950,1066,1218,1303,1404,1496,1593,1709,1831,1937,2070,2203,2337,2501,2629,2753,2883,3003,3096,3193,3314,3437,3535,3638,3747,3888,4037,4146,4246,4330,4424,4519,4606,4693,4794,4874,4960,5057,5160,5253,5350,5438,5543,5640,5739,5859,5939,6041", "endColumns": "114,114,109,114,97,94,111,134,115,151,84,100,91,96,115,121,105,132,132,133,163,127,123,129,119,92,96,120,122,97,102,108,140,148,108,99,83,93,94,86,86,100,79,85,96,102,92,96,87,104,96,98,119,79,101,92", "endOffsets": "165,280,390,505,603,698,810,945,1061,1213,1298,1399,1491,1588,1704,1826,1932,2065,2198,2332,2496,2624,2748,2878,2998,3091,3188,3309,3432,3530,3633,3742,3883,4032,4141,4241,4325,4419,4514,4601,4688,4789,4869,4955,5052,5155,5248,5345,5433,5538,5635,5734,5854,5934,6036,6129"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1484,1599,1714,1824,1939,2037,2132,2244,2379,2495,2647,2732,2833,2925,3022,3138,3260,3366,3499,3632,3766,3930,4058,4182,4312,4432,4525,4622,4743,4866,4964,5067,5176,5317,5466,5575,5675,5759,5853,5948,6035,6122,6223,6303,6389,6486,6589,6682,6779,6867,6972,7069,7168,7288,7368,7470", "endColumns": "114,114,109,114,97,94,111,134,115,151,84,100,91,96,115,121,105,132,132,133,163,127,123,129,119,92,96,120,122,97,102,108,140,148,108,99,83,93,94,86,86,100,79,85,96,102,92,96,87,104,96,98,119,79,101,92", "endOffsets": "1594,1709,1819,1934,2032,2127,2239,2374,2490,2642,2727,2828,2920,3017,3133,3255,3361,3494,3627,3761,3925,4053,4177,4307,4427,4520,4617,4738,4861,4959,5062,5171,5312,5461,5570,5670,5754,5848,5943,6030,6117,6218,6298,6384,6481,6584,6677,6774,6862,6967,7064,7163,7283,7363,7465,7558"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7a9eb4a1e8c3752182f0d47366147a11\\transformed\\foundation-release\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,139", "endColumns": "83,86", "endOffsets": "134,221"}, "to": {"startLines": "83,84", "startColumns": "4,4", "startOffsets": "8475,8559", "endColumns": "83,86", "endOffsets": "8554,8641"}}]}]}